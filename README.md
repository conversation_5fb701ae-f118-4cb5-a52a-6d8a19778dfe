# LOT2 Extension Project / LOT2 扩展项目

## Overview / 项目简介

This repository is a monorepo containing a browser extension, a React-based management frontend, and a Go backend API. The system is designed for automated file monitoring, download, and management, with support for custom rules, scripts, and logging.

本仓库为一个包含浏览器扩展、React 管理前端和 Go 后端 API 的单体仓库。系统用于自动化文件监控、下载与管理，支持自定义规则、脚本和日志管理。

---

## Monorepo Structure / 目录结构

- `axure-manager-ui/` — React + Vite 管理前端 (Frontend UI)
- `extension/` — 浏览器扩展 (Browser Extension)
- `go-backend/` — Go 后端 API 服务 (Go Backend API)

---

## Subprojects / 子项目

### 1. Frontend: axure-manager-ui
- Modern React + Vite + TailwindCSS UI for managing Axure paths and rules.
- See [`axure-manager-ui/README.md`](./axure-manager-ui/README.md) for details.

现代 React + Vite + TailwindCSS 前端，用于管理 Axure 路径和规则。
详见 [`axure-manager-ui/README.md`](./axure-manager-ui/README.md)。

### 2. Extension: extension
- Chrome-compatible browser extension for rule automation and interaction with backend.
- See [`extension/README.md`](./extension/README.md) for details.

Chrome 兼容浏览器扩展，实现规则自动化并与后端交互。
详见 [`extension/README.md`](./extension/README.md)。

### 3. Backend: go-backend
- Go API server for rules, todos, categories, tags, and Axure file management.
- See [`go-backend/README.md`](./go-backend/README.md) for details.

Go 语言 API 服务，提供规则、待办、分类、标签和 Axure 文件管理。
详见 [`go-backend/README.md`](./go-backend/README.md)。

---

## Quick Start / 快速开始

See each subproject's README for setup and usage instructions.

各子项目的使用与部署方法请参见对应 README。

---

## Existing Content / 原有内容

# 项目说明：浏览器扩展工具
## 项目架构
项目采用前后端分离架构：

### 前端（浏览器插件）
- 执行用户交互及自动化操作
- 部分功能在前端独立完成
- 部分功能需要后端配合完成
- 操作日志在浏览器中缓存，定期或启动时回传给后端
### 后端（Go服务）
- 提供API支持前端操作
- 记录并管理操作日志
- 接收前端回传的日志数据
- 对日志进行排重处理
- 支持日志归档功能
## 功能特性
本扩展工具支持以下核心功能（可扩展）：

1. 自动化文件监控与下载
   
   - 模拟用户操作
   - 定时刷新指定界面
   - 检测文件更新
   - 自动下载目标文件

2. 自定义页面样式
   
   - 为指定网站设置特定样式
   - 支持通配符匹配模式
   - 支持针对特定元素设置样式

3. 自定义页面脚本
   
   - 为指定网站注入特定脚本
   - 支持通配符匹配模式
   - 支持针对特定元素执行脚本

4. 自定义快捷键

   - 为指定网站设置特定快捷键
   - 支持通配符匹配模式
   - 支持自定义快捷键功能

5. 日志管理
   - 记录并管理操作日志
   - 支持日志归档功能
   - 支持日志查询功能

## 技术实现
- 前端：Chrome扩展API
- 后端：Go语言服务
- 数据交互：RESTful API
- 日志管理：结构化存储与处理