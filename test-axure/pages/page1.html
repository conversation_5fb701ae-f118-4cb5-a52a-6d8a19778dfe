<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page 1 - Axure Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 30px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .nav-button {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px;
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .content-box {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 Page 1 - Subpage Test</h1>
        
        <div class="content-box">
            <h3>🎯 Navigation Test</h3>
            <p>This page tests navigation within subdirectories and demonstrates that the file serving works for nested folder structures.</p>
        </div>
        
        <div class="content-box">
            <h3>🔗 Links</h3>
            <a href="../index.html" class="nav-button">← Back to Main Page</a>
            <a href="page2.html" class="nav-button">Go to Page 2 →</a>
        </div>
        
        <div class="content-box">
            <h3>📊 Dynamic Content</h3>
            <p>Current time: <span id="current-time"></span></p>
            <button class="nav-button" onclick="updateTime()">Update Time</button>
        </div>
        
        <div class="content-box">
            <h3>📁 File Path</h3>
            <p><code>/Users/<USER>/syncthing/project/lot2extension/test-axure/pages/page1.html</code></p>
        </div>
    </div>
    
    <script>
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        
        // Initialize time on page load
        updateTime();
        
        // Update time every second
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
