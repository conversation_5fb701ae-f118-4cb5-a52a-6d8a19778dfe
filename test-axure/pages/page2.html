<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page 2 - Axure Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 30px;
            background: rgba(0,0,0,0.3);
            border-radius: 15px;
        }
        .interactive-demo {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .color-box {
            width: 100px;
            height: 100px;
            margin: 10px;
            display: inline-block;
            border-radius: 10px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .color-box:hover {
            transform: scale(1.1);
        }
        button {
            padding: 10px 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Page 2 - Interactive Demo</h1>
        
        <div class="interactive-demo">
            <h3>🎯 Interactive Color Demo</h3>
            <p>Click on the colored boxes below to change the page background:</p>
            <div class="color-box" style="background: #ff6b6b;" onclick="changeBackground('#ff6b6b')"></div>
            <div class="color-box" style="background: #4ecdc4;" onclick="changeBackground('#4ecdc4')"></div>
            <div class="color-box" style="background: #45b7d1;" onclick="changeBackground('#45b7d1')"></div>
            <div class="color-box" style="background: #96ceb4;" onclick="changeBackground('#96ceb4')"></div>
            <div class="color-box" style="background: #feca57;" onclick="changeBackground('#feca57')"></div>
        </div>
        
        <div class="interactive-demo">
            <h3>📊 Counter Demo</h3>
            <p>Counter: <span id="counter">0</span></p>
            <button onclick="incrementCounter()">+1</button>
            <button onclick="decrementCounter()">-1</button>
            <button onclick="resetCounter()">Reset</button>
        </div>
        
        <div class="interactive-demo">
            <h3>🔗 Navigation</h3>
            <a href="page1.html" style="color: white;">← Back to Page 1</a> | 
            <a href="../index.html" style="color: white;">🏠 Home</a>
        </div>
        
        <div class="interactive-demo">
            <h3>📁 File Info</h3>
            <p><strong>File:</strong> page2.html</p>
            <p><strong>Directory:</strong> test-axure/pages/</p>
            <p><strong>Last Modified:</strong> <span id="last-modified"></span></p>
        </div>
    </div>
    
    <script>
        let counter = 0;
        
        function changeBackground(color) {
            document.body.style.background = `linear-gradient(45deg, ${color}, #333)`;
        }
        
        function incrementCounter() {
            counter++;
            document.getElementById('counter').textContent = counter;
        }
        
        function decrementCounter() {
            counter--;
            document.getElementById('counter').textContent = counter;
        }
        
        function resetCounter() {
            counter = 0;
            document.getElementById('counter').textContent = counter;
        }
        
        // Set last modified time
        document.getElementById('last-modified').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
