<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Axure Project</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .feature {
            margin: 20px 0;
            padding: 15px;
            background: #e8f4fd;
            border-left: 4px solid #007acc;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background: #007acc;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .button:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Axure Project</h1>
        <p>This is a test HTML file to verify that the Axure file serving functionality works correctly.</p>
        
        <div class="feature">
            <h3>✅ File Serving Test</h3>
            <p>If you can see this page in an iframe within the Axure Manager UI, then the file serving functionality is working correctly!</p>
        </div>
        
        <div class="feature">
            <h3>🔧 Features Tested</h3>
            <ul>
                <li>Path validation and security checks</li>
                <li>Proper MIME type detection for HTML files</li>
                <li>Iframe embedding with correct headers</li>
                <li>File browsing and navigation</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🚀 Interactive Elements</h3>
            <p>Test that JavaScript works in the iframe:</p>
            <button class="button" onclick="alert('JavaScript is working!')">Test JavaScript</button>
            <button class="button" onclick="changeColor()">Change Background</button>
        </div>
        
        <div class="feature">
            <h3>📁 File Structure</h3>
            <p>This test file is located at: <code>/Users/<USER>/syncthing/project/lot2extension/test-axure/index.html</code></p>
        </div>
    </div>
    
    <script>
        function changeColor() {
            const colors = ['#e8f4fd', '#fde8e8', '#e8fde8', '#fdf4e8', '#f4e8fd'];
            const features = document.querySelectorAll('.feature');
            features.forEach(feature => {
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                feature.style.backgroundColor = randomColor;
            });
        }
        
        // Add some dynamic content to show the current time
        document.addEventListener('DOMContentLoaded', function() {
            const timeElement = document.createElement('p');
            timeElement.innerHTML = '<strong>Page loaded at:</strong> ' + new Date().toLocaleString();
            timeElement.style.textAlign = 'center';
            timeElement.style.color = '#666';
            timeElement.style.fontSize = '14px';
            document.querySelector('.container').appendChild(timeElement);
        });
    </script>
</body>
</html>
