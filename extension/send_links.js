// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Send back to the popup a sorted deduped list of valid link URLs on this page.
// The popup injects this script into all frames in the active tab.
console.log('send_links.js');
var links = [].slice.apply(document.querySelectorAll('[data-e2e="scroll-list"] > li'));
links = links.map(function(element) {
  // Return an anchor's href attribute, stripping any URL fragment (hash '#').
  // If the html specifies a relative path, chrome converts it to an absolute
  // URL.
  console.log(element)
  // var href = element.href;
  // var hashIndex = href.indexOf('#');
  // if (hashIndex >= 0) {
  //   href = href.substr(0, hashIndex);
  // }
  return element.src;
});

links.sort();

chrome.runtime.sendMessage(
  links
)

// chrome.extension.sendRequest(links);