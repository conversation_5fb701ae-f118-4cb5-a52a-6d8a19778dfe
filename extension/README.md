# LOT2 Extension / LOT2 浏览器扩展

## Overview / 项目简介

A Chrome-compatible browser extension for automating rule management, script/style injection, and communication with the backend API.

一个兼容 Chrome 的浏览器扩展，实现规则管理、脚本/样式注入和与后端 API 的通信。

---

## Features / 功能特性

- Manage and sync rules with backend
- Inject custom scripts/styles into target websites
- Download links and interact with Douyin pages
- Popup UI for quick actions

- 管理并同步规则到后端
- 向目标网站注入自定义脚本/样式
- 下载链接，支持抖音页面操作
- 弹窗界面，便捷操作

---

## Directory Structure / 目录结构

```
extension/
  background.js      # 后台脚本
  douyin.js          # 抖音页面脚本
  manifest.json      # 扩展清单
  popup.html         # 弹窗界面
  popup.js           # 弹窗逻辑
  send_links.js      # 链接采集脚本
  package.json       # 依赖
```

---

## Permissions / 权限

- cookies, storage, scripting, downloads, nativeMessaging, activeTab
- Host permissions: `http://localhost:8080/*`

---

## Setup & Usage / 安装与使用

1. Build or copy all files to a directory.
2. In Chrome, go to `chrome://extensions/` and enable Developer Mode.
3. Click "Load unpacked" and select the `extension/` directory.
4. Use the popup to manage rules, inject scripts/styles, and interact with backend.

1. 构建或复制所有文件到一个目录。
2. 在 Chrome 地址栏输入 `chrome://extensions/`，开启开发者模式。
3. 点击“加载已解压的扩展程序”，选择 `extension/` 目录。
4. 通过弹窗界面管理规则、注入脚本/样式并与后端交互。

---

## Backend Interaction / 后端交互

- Communicates with the Go backend API for rule management and logging.
- 通过 API 与 Go 后端进行规则管理和日志同步。

---

## License / 许可证

MIT 