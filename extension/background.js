let isConnected = false;
let reconnectSeconds = 5;
let reconnectTimeout<PERSON><PERSON>ler = null;
let socket = null;
chrome.runtime.onInstalled.addListener(function() {
    chrome.storage.sync.set({
      enabled: false
    }, function() {
      console.log('Plugin disabled by default');
    });
  });
  
  chrome.runtime.onStartup.addListener(function() {
    chrome.storage.sync.get(['enabled'], function(result) {
      if (result.enabled) {
        console.log('Plugin enabled on startup');
      }
    });
  });

  function updatePluginBadge()
  {
    // 使用 Mv3 规范
    chrome.action.setBadgeText({text: isConnected ? 'ON' : 'OFF'});
    // set text color to white
    chrome.action.setBadgeTextColor({color: '#ffffff'});
    chrome.action.setBadgeBackgroundColor({color: isConnected ? '#4688F1' : '#333333'});
  }
  