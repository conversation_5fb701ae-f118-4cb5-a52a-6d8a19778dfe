// 规则管理
let rules = [];
let baseUrl = 'https://ext.hopemay.work';
// 从后端获取规则
async function fetchRules() {
  try {
    const response = await fetch(baseUrl+'/api/rules');
    if (!response.ok) throw new Error('Failed to fetch rules');
    rules = await response.json();
    renderRules();
  } catch (error) {
    console.error('Error fetching rules:', error);
  }
}

document.addEventListener('DOMContentLoaded', () => {
  fetchRules();
  
  const addRuleBtn = document.getElementById('add-rule');
  const ruleForm = document.getElementById('rule-form');
  
  if (!addRuleBtn || !ruleForm) {
    console.error('Required DOM elements not found');
    return;
  }
  
  addRuleBtn.addEventListener('click', () => {
    ruleForm.style.display = ruleForm.style.display === 'none' ? 'block' : 'none';
  });
});

// 保存规则到后端
async function saveRule(rule) {
  try {
    const response = await fetch(baseUrl+'/api/rules', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: JSON.stringify(rule)
    });
    if (!response.ok) throw new Error('Failed to save rule');
  } catch (error) {
    console.error('Error saving rule:', error);
  }
}

// 添加规则
function addRule(rule) {
  rules.push(rule);
  renderRules();
}

// 删除规则
function deleteRule(index) {
  rules.splice(index, 1);
  renderRules();
}

// 切换规则状态
function toggleRule(index) {
  rules[index].enabled = !rules[index].enabled;
  fetchRules();
}

// 渲染规则列表
function renderRules() {
  const rulesList = document.getElementById('rules-list');
  rulesList.innerHTML = '';

  if (rules.length === 0) {
    const noDataMessage = document.createElement('div');
    noDataMessage.className = 'no-data';
    noDataMessage.textContent = '暂无数据';
    rulesList.appendChild(noDataMessage);
    return;
  }

  rules.forEach((rule, index) => {
    const ruleItem = document.createElement('div');
    ruleItem.className = 'rule-item';

    const enableCheckbox = document.createElement('input');
    enableCheckbox.type = 'checkbox';
    enableCheckbox.className = 'rule-enable';
    enableCheckbox.checked = rule.enabled;
    enableCheckbox.addEventListener('change', () => toggleRule(index));

    const ruleUrl = document.createElement('span');
    ruleUrl.className = 'rule-url';
    ruleUrl.textContent = rule.url;

    const deleteButton = document.createElement('button');
    deleteButton.className = 'rule-delete';
    deleteButton.textContent = '删除';
    deleteButton.addEventListener('click', () => deleteRule(index));

    ruleItem.appendChild(enableCheckbox);
    ruleItem.appendChild(ruleUrl);
    ruleItem.appendChild(deleteButton);
    rulesList.appendChild(ruleItem);
  });
}

// 初始化
const addRuleBtn = document.getElementById('add-rule');
if (addRuleBtn) {
  addRuleBtn.addEventListener('click', () => {
    const url = prompt('请输入目标网址：');
    if (url) {
      const newRule = {
        url: url,
        enabled: true
      };
      addRule(newRule);
      saveRule(newRule);
    }
  });
}

fetchRules();

const applyScriptStyleBtn = document.getElementById('applyScriptStyle');
if (applyScriptStyleBtn) {
  applyScriptStyleBtn.addEventListener('click', function() {
    const targetWebsite = document.getElementById('targetWebsite').value;
    const scriptStyleContent = document.getElementById('scriptStyleContent').value;
    const wildcardMatch = document.getElementById('wildcardMatch').checked;
    const targetElement = document.getElementById('targetElement').value;

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'applyScriptStyle',
        targetWebsite: targetWebsite,
        scriptStyleContent: scriptStyleContent,
        wildcardMatch: wildcardMatch,
        targetElement: targetElement
      });
    });
  });
}