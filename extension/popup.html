
<!DOCTYPE html>
<head>
  <meta charset="UTF-8">
<script src='popup.js'></script>
</head>
<body>
  <h1>规则管理</h1>
<div id="rules-list">
  <div class="rule-item">
    <div>loading 加载中</div>
  </div>
</div>
<button id="add-rule">新增规则</button>
<div id="rule-form" style="display:none;">
  <label>
    Target Website:
    <input type="text" id="targetWebsite" placeholder="e.g. https://example.com">
  </label>
  <label>
    Script/Style Content:
    <textarea id="scriptStyleContent" rows="5" placeholder="Enter your script or CSS here"></textarea>
  </label>
  <button id="applyScriptStyle">Apply</button>
  <div>
    <input type="checkbox" id="wildcardMatch"> Wildcard Match
  </div>
  <div>
    <label>
      Target Element Selector (optional):
      <input type="text" id="targetElement" placeholder="e.g. .my-class or #my-id">
    </label>
  </div>
  <input type=text id=filter placeholder=Filter>
  <input type=checkbox id=regex>
  <label for=regex>Regex</label><br>
  <button id=download0>Download All!</button>
  <table id=links>
    <tr>
      <th><input type=checkbox checked id=toggle_all></th>
      <th align=left>URL</th>
    </tr>
  </table>
  <button id=download1>Download All!</button>
</body>
<style>
#rule-form {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

body {
  min-width: 400px;
  min-height: 500px;
}
</style>
</html>
