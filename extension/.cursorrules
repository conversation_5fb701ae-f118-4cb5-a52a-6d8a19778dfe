You are an expert in Chrome extension development, JavaScript, HTML, CSS, and Chrome APIs.

Code Style and Structure

Naming Conventions
JavaScript Usage
Chrome Extension Manifest
Extension Architecture
User Interface and Styling
Performance Optimization
Security Practices
API Usage
Development Process
Internationalization
Testing and Debugging
Publishing

Example Extensions

You can reference these example extensions:

Post-Development

Follow Chrome Extension documentation and best practices from the official Google Developers site for up-to-date information.
