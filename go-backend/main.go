package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gorilla/mux"
	_ "github.com/go-sql-driver/mysql"

	"github.com/LosEcher/lot2extension/go-backend/internal/config"
	"github.com/LosEcher/lot2extension/go-backend/internal/database"
	"github.com/LosEcher/lot2extension/go-backend/internal/handlers"
	"github.com/LosEcher/lot2extension/go-backend/internal/middleware"
)

// responseWriterWrapper is defined in utils.go

// createLogsDir creates the logs directory with proper permissions
func createLogsDir() {
	logDir := "/app/logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		log.Fatalf("Failed to create logs directory: %v", err)
	}
	// Try to set ownership to the current user
	if err := os.Chmod(logDir, 0755); err != nil {
		log.Printf("Warning: Failed to set permissions on logs directory: %v", err)
	}
}

func main() {
	// Handle command-line flags
	createLogs := flag.Bool("create-logs-dir", false, "Create logs directory with proper permissions")
	configPath := flag.String("config", "config.yaml", "Path to configuration file")
	flag.Parse()

	// Handle special commands
	if *createLogs {
		createLogsDir()
		return
	}

	// Load configuration
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connections
	db, err := database.Initialize(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer func() {
		if err := db.Close(); err != nil {
			log.Printf("Error closing database connections: %v", err)
		}
	}()

	// Initialize file service
	fileService := handlers.NewFileService()

	// Create a new router
	router := mux.NewRouter()

	// Register all handlers
	handlers.RegisterBasicHandlers(router)
	handlers.RegisterAPIHandlers(router, db, cfg)

	// The Axure file browsing service is separate from the path management API
	// and is defined in axure.go. We'll assume a function like this exists.
	if err := handlers.RegisterAxureFileServiceHandlers(router, fileService); err != nil {
		log.Printf("Could not register Axure file service handlers: %v", err)
	}

	// Set up middleware chain
	var handler http.Handler = router

	// Create rate limiter (60 requests per minute, burst of 10)
	rateLimiter := middleware.NewRateLimiter(60, 10)

	// Apply middleware with configuration (order matters!)
	handler = middleware.RequestIDMiddleware(handler)
	handler = middleware.SecurityHeadersMiddleware(handler)
	handler = middleware.CORSMiddleware(cfg.Security.AllowedCORS)(handler)
	handler = middleware.RequestSizeLimitMiddleware(10 * 1024 * 1024)(handler) // 10MB limit
	handler = middleware.RateLimitMiddleware(rateLimiter)(handler)
	handler = middleware.LoggingMiddleware(handler)
	handler = middleware.AuthMiddleware(handler, cfg.Security.APIKey)

	// Configure server with timeouts from config
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      handler,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Setup graceful shutdown
	go func() {
		log.Printf("Server starting on :%d", cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server error: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}
