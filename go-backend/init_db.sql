CREATE DATABASE `dyrecords` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 规则表存储所有URL规则
CREATE DATABASE IF NOT EXISTS dyrecords;
USE dyrecords;

-- 规则表存储所有URL规则
CREATE TABLE IF NOT EXISTS rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(255) NOT NULL COMMENT '规则URL',
    enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    created_by VARCHAR(64) DEFAULT 'system' COMMENT '创建人',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updated_by VARCHAR(64) DEFAULT 'system' COMMENT '更新人',
    last_access TIMESTAMP NULL COMMENT '最后访问时间',
    access_count INT DEFAULT 0 COMMENT '访问次数',
    is_secure BOOLEAN DEFAULT FALSE COMMENT '是否安全连接',
    -- 添加索引
    INDEX idx_url (url),
    INDEX idx_enabled (enabled),
    INDEX idx_is_secure (is_secure)
) ENGINE=InnoDB COMMENT='URL规则表';

-- 分类表
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_name (name)
) ENGINE=InnoDB COMMENT='分类表';

-- 标签表
CREATE TABLE IF NOT EXISTS tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    color VARCHAR(7) DEFAULT '#007bff' COMMENT '标签颜色',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_name (name)
) ENGINE=InnoDB COMMENT='标签表';

-- 待办事项表
CREATE TABLE IF NOT EXISTS todos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT COMMENT '内容描述',
    completed BOOLEAN DEFAULT FALSE COMMENT '是否完成',
    due_date DATETIME NULL COMMENT '截止日期',
    category_id INT NULL COMMENT '分类ID',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium' COMMENT '优先级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    -- 外键约束
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    -- 索引
    INDEX idx_completed (completed),
    INDEX idx_due_date (due_date),
    INDEX idx_category_id (category_id),
    INDEX idx_priority (priority)
) ENGINE=InnoDB COMMENT='待办事项表';

-- 待办事项标签关联表
CREATE TABLE IF NOT EXISTS todo_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    todo_id INT NOT NULL COMMENT '待办事项ID',
    tag_id INT NOT NULL COMMENT '标签ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    -- 外键约束
    FOREIGN KEY (todo_id) REFERENCES todos(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    -- 唯一约束，防止重复关联
    UNIQUE KEY uk_todo_tag (todo_id, tag_id),
    -- 索引
    INDEX idx_todo_id (todo_id),
    INDEX idx_tag_id (tag_id)
) ENGINE=InnoDB COMMENT='待办事项标签关联表';

-- 主日志表 (不再按月分片)
CREATE TABLE IF NOT EXISTS logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    level ENUM('debug', 'info', 'warn', 'error', 'fatal') NOT NULL COMMENT '日志级别',
    message TEXT NOT NULL COMMENT '日志内容',
    context JSON COMMENT '上下文数据',
    request_id VARCHAR(64) COMMENT '请求ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    ip VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    method VARCHAR(10) COMMENT 'HTTP方法',
    path VARCHAR(255) COMMENT '请求路径',
    status INT COMMENT 'HTTP状态码',
    latency INT COMMENT '延迟(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_level (level),
    INDEX idx_created_at (created_at),
    INDEX idx_request_id (request_id),
    INDEX idx_user_id (user_id),
    INDEX idx_path (path)
) ENGINE=InnoDB COMMENT='主日志表';

-- 归档日志表结构 (保留用于手动归档)
CREATE TABLE IF NOT EXISTS logs_archive LIKE logs;

-- Axure项目路径表
CREATE TABLE IF NOT EXISTS axure_paths (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT '路径的自定义名称或别名',
    path VARCHAR(1024) NOT NULL COMMENT 'Axure项目目录的绝对文件系统路径',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_path (path(191))
) ENGINE=InnoDB COMMENT='存储管理的Axure文件夹路径';

-- 删除按月分表相关的存储过程和表
DROP PROCEDURE IF EXISTS create_log_shard;
DROP TABLE IF EXISTS log_shards;

-- 初始化测试数据
INSERT INTO rules (url, enabled, created_by, updated_by, is_secure) VALUES
('https://example.com/rule1', TRUE, 'admin', 'admin', TRUE),
('https://example.com/rule2', FALSE, 'user', 'user', FALSE);

-- 初始化分类数据
INSERT INTO categories (name, description) VALUES
('工作', '工作相关的待办事项'),
('个人', '个人生活相关的待办事项'),
('学习', '学习和培训相关的待办事项'),
('项目', '项目管理相关的待办事项');

-- 初始化标签数据
INSERT INTO tags (name, color) VALUES
('紧急', '#dc3545'),
('重要', '#fd7e14'),
('会议', '#6f42c1'),
('开发', '#28a745'),
('测试', '#17a2b8'),
('文档', '#6c757d'),
('bug修复', '#e83e8c'),
('功能开发', '#20c997');

-- 初始化待办事项数据
INSERT INTO todos (title, content, completed, due_date, category_id, priority) VALUES
('完成API开发', '完成后端API的开发和测试', FALSE, '2024-01-15 18:00:00', 1, 'high'),
('代码审查', '审查团队成员提交的代码', FALSE, '2024-01-12 17:00:00', 1, 'medium'),
('购买生活用品', '去超市购买本周所需的生活用品', FALSE, '2024-01-13 20:00:00', 2, 'low'),
('学习新技术', '学习Go语言的高级特性', FALSE, '2024-01-20 23:59:59', 3, 'medium');

-- 初始化待办事项标签关联数据
INSERT INTO todo_tags (todo_id, tag_id) VALUES
(1, 4), -- 完成API开发 - 开发
(1, 2), -- 完成API开发 - 重要
(2, 4), -- 代码审查 - 开发
(2, 6), -- 代码审查 - 文档
(3, 1), -- 购买生活用品 - 紧急
(4, 4), -- 学习新技术 - 开发
(4, 2); -- 学习新技术 - 重要