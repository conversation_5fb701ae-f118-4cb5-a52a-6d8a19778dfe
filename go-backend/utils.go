package main

import (
	"encoding/json"
	"log"
	"net/http"
	"strings"
	"regexp"
)

// setCorsHeaders sets the CORS headers for the response
func setCorsHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "chrome-extension://*")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.<PERSON>er().Set("Access-Control-Allow-Headers", "Content-Type, X-API-KEY")
}

// responseWriterWrapper wraps http.ResponseWriter to capture the status code
type responseWriterWrapper struct {
	http.ResponseWriter
	request *http.Request
	status  int
}

// WriteHeader captures the status code before writing the header
func (rw *responseWriterWrapper) WriteHeader(statusCode int) {
	rw.status = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

// sendErrorResponse sends a JSON error response with the given message and status code
func sendErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	setCorsHeaders(w)
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.Write<PERSON>eader(statusCode)
	errResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}
	if err := json.NewEncoder(w).Encode(errResponse); err != nil {
		log.Printf("Failed to send error response: %v", err)
	}
}

// isValidEndpoint validates if the given endpoint has a valid format
func isValidEndpoint(endpoint string) bool {
	// Endpoint should start with /axure/files/
	if !strings.HasPrefix(endpoint, "/axure/files/") {
		return false
	}

	// Remove the prefix for further validation
	path := strings.TrimPrefix(endpoint, "/axure/files/")

	// Check if the remaining path is not empty
	if path == "" {
		return false
	}

	// Validate path contains only allowed characters (alphanumeric, hyphen, underscore, dot)
	// and doesn't contain any path traversal attempts
	validPath := regexp.MustCompile(`^[a-zA-Z0-9\-_.]+$`).MatchString
	if !validPath(path) {
		return false
	}

	return true
}
