# API Documentation

## Overview

This is a RESTful API service built with Go that provides functionality for managing URL rules, todos, categories, and tags. The API includes authentication, rate limiting, and comprehensive error handling.

## Base URL

```
http://localhost:8086
```

## Authentication

All API endpoints require authentication using an API key passed in the request header:

```
X-API-KEY: your_secret_key_here
```

## Rate Limiting

The API implements rate limiting with the following defaults:
- **Rate**: 60 requests per minute
- **Burst**: 10 requests
- **Response**: HTTP 429 when limit exceeded

## Request/Response Format

### Content Type
All requests and responses use `application/json` content type.

### Standard Response Format

#### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details",
    "validation": [
      {
        "field": "field_name",
        "message": "Validation error message"
      }
    ]
  },
  "request_id": "unique_request_id",
  "timestamp": "2023-12-07T10:30:00Z"
}
```

## Endpoints

### Health Check

#### GET /ping
Simple health check endpoint.

**Response:**
```json
{
  "success": true,
  "message": "pong"
}
```

#### GET /api/health
Comprehensive health check including database connectivity.

**Response:**
```json
{
  "status": "ok",
  "checks": {
    "database": {
      "status": "ok"
    }
  }
}
```

### Rules Management

#### GET /api/rules
Retrieve all URL rules.

**Response:**
```json
{
  "success": true,
  "rules": [
    {
      "url": "https://example.com",
      "enabled": true,
      "is_secure": true,
      "created_at": "2023-12-07T10:30:00Z",
      "updated_at": "2023-12-07T10:30:00Z",
      "created_by": "user123",
      "description": "Example rule"
    }
  ],
  "count": 1
}
```

#### POST /api/rules/add
Add a new URL rule.

**Request Body:**
```json
{
  "url": "https://example.com",
  "enabled": true,
  "is_secure": true,
  "description": "Example rule",
  "created_by": "user123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Rule added successfully"
}
```

#### PUT /api/rules/update
Update an existing URL rule.

**Request Body:**
```json
{
  "url": "https://example.com",
  "enabled": false,
  "description": "Updated description"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Rule updated successfully"
}
```

#### DELETE /api/rules/delete
Delete a URL rule.

**Query Parameters:**
- `url` (required): The URL of the rule to delete

**Response:**
```json
{
  "success": true,
  "message": "Rule deleted successfully"
}
```

### Todos Management

#### GET /api/todos
Retrieve all todos.

**Response:**
```json
{
  "success": true,
  "todos": [
    {
      "id": 1,
      "title": "Example Todo",
      "content": "This is an example todo",
      "completed": false,
      "due_date": "2023-12-08T10:30:00Z",
      "category_id": 1,
      "tags": ["work", "important"],
      "created_at": "2023-12-07T10:30:00Z",
      "updated_at": "2023-12-07T10:30:00Z"
    }
  ],
  "count": 1
}
```

#### POST /api/todos/add
Add a new todo.

**Request Body:**
```json
{
  "title": "New Todo",
  "content": "Todo description",
  "due_date": "2023-12-08T10:30:00Z",
  "category_id": 1,
  "tags": ["work", "important"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Todo added successfully",
  "todo": { ... }
}
```

#### PUT /api/todos/update
Update an existing todo.

**Request Body:**
```json
{
  "id": 1,
  "title": "Updated Todo",
  "content": "Updated description",
  "completed": true,
  "due_date": "2023-12-08T10:30:00Z",
  "category_id": 1,
  "tags": ["work", "completed"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Todo updated successfully",
  "todo": { ... }
}
```

#### DELETE /api/todos/delete
Delete a todo.

**Query Parameters:**
- `id` (required): The ID of the todo to delete

**Response:**
```json
{
  "success": true,
  "message": "Todo deleted successfully"
}
```

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `BAD_REQUEST` | 400 | Invalid request format or parameters |
| `UNAUTHORIZED` | 401 | Missing or invalid API key |
| `FORBIDDEN` | 403 | Access denied |
| `NOT_FOUND` | 404 | Resource not found |
| `CONFLICT` | 409 | Resource already exists |
| `VALIDATION_ERROR` | 400 | Input validation failed |
| `TOO_MANY_REQUESTS` | 429 | Rate limit exceeded |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

## Validation Rules

### URL Rules
- `url`: Required, must be a valid URL, max 2048 characters
- `description`: Optional, max 500 characters
- `created_by`: Optional, max 100 characters

### Todos
- `title`: Required, max 255 characters
- `content`: Optional, max 2000 characters
- `due_date`: Optional, cannot be more than 1 year in the past
- `category_id`: Optional, must be positive integer
- `tags`: Optional, max 10 tags, each max 50 characters

## Security Headers

The API includes the following security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Content-Security-Policy: default-src 'self'`

## CORS

Cross-Origin Resource Sharing (CORS) is configured to allow:
- Chrome extensions (`chrome-extension://*`)
- Configurable allowed origins
- Methods: GET, POST, PUT, DELETE, OPTIONS
- Headers: Content-Type, X-API-KEY, Authorization, X-Request-ID

## Request Tracking

Each request receives a unique request ID that is:
- Included in response headers as `X-Request-ID`
- Logged for debugging purposes
- Included in error responses

## Examples

### cURL Examples

#### Get all rules
```bash
curl -H "X-API-KEY: your_key" http://localhost:8086/api/rules
```

#### Add a new rule
```bash
curl -X POST \
  -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://example.com","enabled":true}' \
  http://localhost:8086/api/rules/add
```

#### Add a new todo
```bash
curl -X POST \
  -H "X-API-KEY: your_key" \
  -H "Content-Type: application/json" \
  -d '{"title":"New Task","content":"Task description"}' \
  http://localhost:8086/api/todos/add
```
