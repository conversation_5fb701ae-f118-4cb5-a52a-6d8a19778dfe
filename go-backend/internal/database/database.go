package database

import (
	"database/sql"
	"fmt"
	"log"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/gomodule/redigo/redis"

	"github.com/LosEcher/lot2extension/go-backend/internal/config"
)

// DB holds database connections and provides access methods
type DB struct {
	MySQL *sql.DB
	Redis *redis.Pool
	mu    sync.RWMutex
}

var (
	instance *DB
	once     sync.Once
)

// Initialize creates and returns a singleton database instance
func Initialize(cfg *config.Config) (*DB, error) {
	var err error
	once.Do(func() {
		instance = &DB{}
		err = instance.connect(cfg)
	})
	return instance, err
}

// GetInstance returns the singleton database instance
func GetInstance() *DB {
	if instance == nil {
		log.Fatal("Database not initialized. Call Initialize() first.")
	}
	return instance
}

// connect establishes connections to MySQL and Redis
func (db *DB) connect(cfg *config.Config) error {
	// Initialize MySQL connection
	if err := db.connectMySQL(cfg.Database.MySQL); err != nil {
		return fmt.Errorf("failed to connect to MySQL: %w", err)
	}

	// Initialize Redis connection pool
	if err := db.connectRedis(cfg.Redis); err != nil {
		return fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Println("Successfully connected to all databases")
	return nil
}

// connectMySQL establishes MySQL connection with proper configuration
func (db *DB) connectMySQL(cfg config.MySQLConfig) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true&charset=utf8mb4&collation=utf8mb4_unicode_ci",
		cfg.User,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
	)

	var err error
	db.MySQL, err = sql.Open("mysql", dsn)
	if err != nil {
		return err
	}

	// Configure connection pool
	db.MySQL.SetMaxOpenConns(cfg.MaxOpenConns)
	db.MySQL.SetMaxIdleConns(cfg.MaxIdleConns)
	db.MySQL.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// Test the connection
	if err := db.MySQL.Ping(); err != nil {
		return fmt.Errorf("failed to ping MySQL: %w", err)
	}

	log.Printf("MySQL connected successfully to %s:%d/%s", cfg.Host, cfg.Port, cfg.Database)
	return nil
}

// connectRedis establishes Redis connection pool
func (db *DB) connectRedis(cfg config.RedisConfig) error {
	db.Redis = &redis.Pool{
		MaxIdle:     cfg.MaxIdle,
		MaxActive:   cfg.MaxActive,
		IdleTimeout: cfg.IdleTimeout,
		Dial: func() (redis.Conn, error) {
			conn, err := redis.Dial("tcp",
				fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
				redis.DialPassword(cfg.Password),
				redis.DialDatabase(cfg.DB),
				redis.DialConnectTimeout(5*time.Second),
				redis.DialReadTimeout(3*time.Second),
				redis.DialWriteTimeout(3*time.Second),
			)
			if err != nil {
				return nil, fmt.Errorf("failed to connect to Redis: %w", err)
			}
			return conn, nil
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			if time.Since(t) < time.Minute {
				return nil
			}
			_, err := c.Do("PING")
			return err
		},
	}

	// Test Redis connection
	conn := db.Redis.Get()
	defer conn.Close()

	if _, err := conn.Do("PING"); err != nil {
		return fmt.Errorf("failed to ping Redis: %w", err)
	}

	log.Printf("Redis connected successfully to %s:%d", cfg.Host, cfg.Port)
	return nil
}

// GetMySQL returns the MySQL database connection
func (db *DB) GetMySQL() *sql.DB {
	db.mu.RLock()
	defer db.mu.RUnlock()
	return db.MySQL
}

// GetRedis returns a Redis connection from the pool
func (db *DB) GetRedis() redis.Conn {
	return db.Redis.Get()
}

// Close closes all database connections
func (db *DB) Close() error {
	db.mu.Lock()
	defer db.mu.Unlock()

	var errs []error

	if db.MySQL != nil {
		if err := db.MySQL.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close MySQL: %w", err))
		}
	}

	if db.Redis != nil {
		if err := db.Redis.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close Redis: %w", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors closing databases: %v", errs)
	}

	log.Println("All database connections closed successfully")
	return nil
}

// HealthCheck performs health checks on all database connections
func (db *DB) HealthCheck() error {
	// Check MySQL
	if err := db.MySQL.Ping(); err != nil {
		return fmt.Errorf("MySQL health check failed: %w", err)
	}

	// Check Redis
	conn := db.GetRedis()
	defer conn.Close()

	if _, err := conn.Do("PING"); err != nil {
		return fmt.Errorf("Redis health check failed: %w", err)
	}

	return nil
}
