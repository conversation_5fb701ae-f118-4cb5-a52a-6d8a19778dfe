package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestNewRateLimiter(t *testing.T) {
	rl := NewRateLimiter(60, 10)
	
	if rl.rate != 60 {
		t.<PERSON><PERSON><PERSON>("Expected rate 60, got %d", rl.rate)
	}
	
	if rl.burst != 10 {
		t.<PERSON><PERSON><PERSON>("Expected burst 10, got %d", rl.burst)
	}
	
	if rl.visitors == nil {
		t.<PERSON>r("Expected visitors map to be initialized")
	}
}

func TestRateLimiterAllow(t *testing.T) {
	rl := NewRateLimiter(60, 2) // 2 requests burst
	clientIP := "***********"
	
	// First request should be allowed
	if !rl.allow(clientIP) {
		t.Error("First request should be allowed")
	}
	
	// Second request should be allowed (within burst)
	if !rl.allow(clientIP) {
		t.Error("Second request should be allowed")
	}
	
	// Third request should be denied (exceeds burst)
	if rl.allow(clientIP) {
		t.<PERSON>rror("Third request should be denied")
	}
}

func TestRateLimiterTokenRefill(t *testing.T) {
	rl := NewRateLimiter(60, 1) // 1 request per minute, burst of 1
	clientIP := "***********"
	
	// Use up the burst
	if !rl.allow(clientIP) {
		t.Error("First request should be allowed")
	}
	
	// Should be denied immediately
	if rl.allow(clientIP) {
		t.Error("Second request should be denied")
	}
	
	// Manually advance time by updating the visitor's lastSeen
	rl.mu.Lock()
	if visitor, exists := rl.visitors[clientIP]; exists {
		visitor.lastSeen = time.Now().Add(-2 * time.Minute) // 2 minutes ago
	}
	rl.mu.Unlock()
	
	// Should be allowed now (tokens refilled)
	if !rl.allow(clientIP) {
		t.Error("Request should be allowed after token refill")
	}
}

func TestRateLimitMiddleware(t *testing.T) {
	rl := NewRateLimiter(60, 1) // Very restrictive for testing
	middleware := RateLimitMiddleware(rl)
	
	handler := middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	
	// First request should pass
	req1 := httptest.NewRequest("GET", "/test", nil)
	req1.RemoteAddr = "***********:12345"
	rr1 := httptest.NewRecorder()
	
	handler.ServeHTTP(rr1, req1)
	
	if rr1.Code != http.StatusOK {
		t.Errorf("First request should be allowed, got status %d", rr1.Code)
	}
	
	// Second request from same IP should be rate limited
	req2 := httptest.NewRequest("GET", "/test", nil)
	req2.RemoteAddr = "***********:12345" // Same IP as first request
	rr2 := httptest.NewRecorder()

	handler.ServeHTTP(rr2, req2)
	
	if rr2.Code != http.StatusTooManyRequests {
		t.Errorf("Second request should be rate limited, got status %d", rr2.Code)
	}
	
	// Check Retry-After header
	retryAfter := rr2.Header().Get("Retry-After")
	if retryAfter != "60" {
		t.Errorf("Expected Retry-After header to be '60', got '%s'", retryAfter)
	}
}

func TestRateLimiterDifferentIPs(t *testing.T) {
	rl := NewRateLimiter(60, 1)
	
	// Different IPs should have separate limits
	if !rl.allow("***********") {
		t.Error("First IP should be allowed")
	}
	
	if !rl.allow("***********") {
		t.Error("Second IP should be allowed")
	}
	
	// Both IPs should now be at their limit
	if rl.allow("***********") {
		t.Error("First IP should be at limit")
	}
	
	if rl.allow("***********") {
		t.Error("Second IP should be at limit")
	}
}

func TestGetClientIP(t *testing.T) {
	tests := []struct {
		name           string
		remoteAddr     string
		xForwardedFor  string
		xRealIP        string
		expectedResult string
	}{
		{
			name:           "remote addr only",
			remoteAddr:     "***********:12345",
			expectedResult: "***********:12345",
		},
		{
			name:           "x-forwarded-for single",
			remoteAddr:     "***********:12345",
			xForwardedFor:  "***********",
			expectedResult: "***********",
		},
		{
			name:           "x-real-ip",
			remoteAddr:     "***********:12345",
			xRealIP:        "***********",
			expectedResult: "***********",
		},
		{
			name:           "x-forwarded-for takes precedence",
			remoteAddr:     "***********:12345",
			xForwardedFor:  "***********",
			xRealIP:        "***********",
			expectedResult: "***********",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/test", nil)
			req.RemoteAddr = tt.remoteAddr
			
			if tt.xForwardedFor != "" {
				req.Header.Set("X-Forwarded-For", tt.xForwardedFor)
			}
			
			if tt.xRealIP != "" {
				req.Header.Set("X-Real-IP", tt.xRealIP)
			}
			
			result := getClientIP(req)
			if result != tt.expectedResult {
				t.Errorf("Expected %s, got %s", tt.expectedResult, result)
			}
		})
	}
}

func TestRateLimiterStats(t *testing.T) {
	rl := NewRateLimiter(60, 10)
	
	// Initially no visitors
	stats := rl.GetStats()
	if stats["active_visitors"] != 0 {
		t.Errorf("Expected 0 active visitors, got %v", stats["active_visitors"])
	}
	
	// Add a visitor
	rl.allow("***********")
	
	stats = rl.GetStats()
	if stats["active_visitors"] != 1 {
		t.Errorf("Expected 1 active visitor, got %v", stats["active_visitors"])
	}
	
	if stats["rate_per_minute"] != 60 {
		t.Errorf("Expected rate 60, got %v", stats["rate_per_minute"])
	}
	
	if stats["burst_capacity"] != 10 {
		t.Errorf("Expected burst 10, got %v", stats["burst_capacity"])
	}
}

func TestRateLimiterReset(t *testing.T) {
	rl := NewRateLimiter(60, 1)
	
	// Add a visitor
	rl.allow("***********")
	
	stats := rl.GetStats()
	if stats["active_visitors"] != 1 {
		t.Error("Expected 1 active visitor before reset")
	}
	
	// Reset
	rl.Reset()
	
	stats = rl.GetStats()
	if stats["active_visitors"] != 0 {
		t.Error("Expected 0 active visitors after reset")
	}
}

func TestRateLimiterSetters(t *testing.T) {
	rl := NewRateLimiter(60, 10)
	
	// Test SetRate
	rl.SetRate(120)
	stats := rl.GetStats()
	if stats["rate_per_minute"] != 120 {
		t.Errorf("Expected rate 120 after SetRate, got %v", stats["rate_per_minute"])
	}
	
	// Test SetBurst
	rl.SetBurst(20)
	stats = rl.GetStats()
	if stats["burst_capacity"] != 20 {
		t.Errorf("Expected burst 20 after SetBurst, got %v", stats["burst_capacity"])
	}
}

func TestIPWhitelistMiddleware(t *testing.T) {
	whitelist := []string{"***********00"}
	middleware := IPWhitelistMiddleware(whitelist)
	
	handler := middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	
	tests := []struct {
		name       string
		remoteAddr string
		expected   int
	}{
		{"whitelisted IP", "***********00:12345", http.StatusOK},
		{"non-whitelisted IP", "***********:12345", http.StatusOK}, // Still passes through
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/test", nil)
			req.RemoteAddr = tt.remoteAddr
			rr := httptest.NewRecorder()
			
			handler.ServeHTTP(rr, req)
			
			if rr.Code != tt.expected {
				t.Errorf("Expected status %d, got %d", tt.expected, rr.Code)
			}
		})
	}
}
