package middleware

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
)

// RequestIDMiddleware adds a unique request ID to each request
func RequestIDMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestID := generateRequestID()

		// Add request ID to context
		ctx := context.WithValue(r.Context(), "request_id", requestID)
		r = r.WithContext(ctx)

		// Add request ID to response headers
		w.Header().Set("X-Request-ID", requestID)

		next.ServeHTTP(w, r)
	})
}

// RequestSizeLimitMiddleware limits the size of request bodies
func RequestSizeLimitMiddleware(maxSize int64) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.ContentLength > maxSize {
				http.Error(w, "Request body too large", http.StatusRequestEntityTooLarge)
				return
			}

			// Limit the request body reader
			r.Body = http.MaxBytesReader(w, r.Body, maxSize)

			next.ServeHTTP(w, r)
		})
	}
}

// LoggingMiddleware logs information about each HTTP request
func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		ww := &responseWriterWrapper{
			ResponseWriter: w,
			status:         http.StatusOK,
		}

		// Get request ID from context
		requestID := getRequestID(r)

		next.ServeHTTP(ww, r)

		duration := time.Since(start)
		log.Printf("[%s] %s %s - %d in %v (RequestID: %s)",
			r.Method, r.URL.Path, r.RemoteAddr, ww.status, duration, requestID)
	})
}

// AuthMiddleware validates the API key in the request header
func AuthMiddleware(next http.Handler, apiKey string) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r == nil {
			http.Error(w, "Invalid request", http.StatusBadRequest)
			return
		}

		// Handle OPTIONS preflight requests
		if r.Method == http.MethodOptions {
			setCORSHeaders(w)
			w.WriteHeader(http.StatusOK)
			return
		}

		// Skip API key validation for file serving endpoints
		if strings.HasPrefix(r.URL.Path, "/api/axure/serve-file") ||
		   strings.HasPrefix(r.URL.Path, "/api/axure/list-files") {
			setCORSHeaders(w)
			next.ServeHTTP(w, r)
			return
		}

		// Check if API key is configured
		if apiKey == "" {
			// Fallback to environment variable
			apiKey = os.Getenv("API_SECRET_KEY")
			if apiKey == "" {
				log.Println("Warning: API key not configured. All requests will be rejected.")
				setCORSHeaders(w)
				http.Error(w, "Server configuration error", http.StatusInternalServerError)
				return
			}
		}

		providedKey := r.Header.Get("X-API-KEY")
		if providedKey == "" {
			log.Printf("Unauthorized access attempt: %s %s - Missing API key", r.Method, r.URL.Path)
			setCORSHeaders(w)
			http.Error(w, "API key required", http.StatusUnauthorized)
			return
		}

		if !secureCompare(providedKey, apiKey) {
			log.Printf("Unauthorized access attempt: %s %s - Invalid API key", r.Method, r.URL.Path)
			setCORSHeaders(w)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		setCORSHeaders(w)
		next.ServeHTTP(w, r)
	})
}

// responseWriterWrapper wraps http.ResponseWriter to capture the status code
type responseWriterWrapper struct {
	http.ResponseWriter
	status int
}

// WriteHeader captures the status code before writing the header
func (rww *responseWriterWrapper) WriteHeader(statusCode int) {
	rww.status = statusCode
	rww.ResponseWriter.WriteHeader(statusCode)
}

// secureCompare performs a constant-time comparison to prevent timing attacks
func secureCompare(a, b string) bool {
	if len(a) != len(b) {
		return false
	}

	var result byte
	for i := 0; i < len(a); i++ {
		result |= a[i] ^ b[i]
	}
	return result == 0
}

// CORSMiddleware handles CORS headers and preflight requests
func CORSMiddleware(allowedOrigins []string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			origin := r.Header.Get("Origin")

			// Check if origin is allowed
			if isOriginAllowed(origin, allowedOrigins) {
				w.Header().Set("Access-Control-Allow-Origin", origin)
			} else if len(allowedOrigins) > 0 && allowedOrigins[0] == "*" {
				w.Header().Set("Access-Control-Allow-Origin", "*")
			}

			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-API-KEY, Authorization, X-Request-ID")
			w.Header().Set("Access-Control-Expose-Headers", "X-Request-ID")
			w.Header().Set("Access-Control-Max-Age", "86400") // 24 hours

			// Handle preflight requests
			if r.Method == http.MethodOptions {
				w.WriteHeader(http.StatusOK)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// SecurityHeadersMiddleware adds security headers to responses
func SecurityHeadersMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Security headers
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.Header().Set("X-Frame-Options", "DENY")
		w.Header().Set("X-XSS-Protection", "1; mode=block")
		w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
		w.Header().Set("Content-Security-Policy", "default-src 'self'")

		next.ServeHTTP(w, r)
	})
}

// setCORSHeaders sets the CORS headers for the response (legacy function)
func setCORSHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "chrome-extension://*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-API-KEY")
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to timestamp-based ID if random generation fails
		return fmt.Sprintf("req_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// getRequestID extracts the request ID from the context
func getRequestID(r *http.Request) string {
	if requestID, ok := r.Context().Value("request_id").(string); ok {
		return requestID
	}
	return "unknown"
}

// isOriginAllowed checks if an origin is in the allowed list
func isOriginAllowed(origin string, allowedOrigins []string) bool {
	if origin == "" {
		return false
	}

	for _, allowed := range allowedOrigins {
		if allowed == "*" {
			return true
		}
		if strings.HasPrefix(allowed, "chrome-extension://") && strings.HasPrefix(origin, "chrome-extension://") {
			return true
		}
		if allowed == origin {
			return true
		}
		// Support wildcard subdomains
		if strings.HasPrefix(allowed, "*.") {
			domain := strings.TrimPrefix(allowed, "*.")
			if strings.HasSuffix(origin, "."+domain) || origin == domain {
				return true
			}
		}
	}

	return false
}
