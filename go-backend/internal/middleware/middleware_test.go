package middleware

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestRequestIDMiddleware(t *testing.T) {
	handler := RequestIDMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		requestID := getRequestID(r)
		if requestID == "unknown" {
			t.<PERSON>rror("Expected request ID to be set")
		}
		w.<PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
	}))

	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	// Check that X-Request-ID header is set
	requestID := rr.<PERSON>er().Get("X-Request-ID")
	if requestID == "" {
		t.Error("Expected X-Request-ID header to be set")
	}
}

func TestRequestSizeLimitMiddleware(t *testing.T) {
	maxSize := int64(10) // 10 bytes
	middleware := RequestSizeLimitMiddleware(maxSize)
	
	handler := middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON>(http.StatusOK)
	}))

	tests := []struct {
		name           string
		body           string
		expectedStatus int
	}{
		{"small body", "small", http.StatusOK},
		{"large body", "this is a very large body that exceeds the limit", http.StatusRequestEntityTooLarge},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("POST", "/test", strings.NewReader(tt.body))
			req.ContentLength = int64(len(tt.body))
			rr := httptest.NewRecorder()

			handler.ServeHTTP(rr, req)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}
		})
	}
}

func TestAuthMiddleware(t *testing.T) {
	validAPIKey := "test-api-key"
	
	handler := AuthMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}), validAPIKey)

	tests := []struct {
		name           string
		apiKey         string
		expectedStatus int
	}{
		{"valid API key", validAPIKey, http.StatusOK},
		{"invalid API key", "invalid-key", http.StatusUnauthorized},
		{"missing API key", "", http.StatusUnauthorized},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/test", nil)
			if tt.apiKey != "" {
				req.Header.Set("X-API-KEY", tt.apiKey)
			}
			rr := httptest.NewRecorder()

			handler.ServeHTTP(rr, req)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}
		})
	}
}

func TestAuthMiddlewareOptionsRequest(t *testing.T) {
	handler := AuthMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}), "test-key")

	req := httptest.NewRequest("OPTIONS", "/test", nil)
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status %d for OPTIONS request, got %d", http.StatusOK, rr.Code)
	}
}

func TestCORSMiddleware(t *testing.T) {
	allowedOrigins := []string{"https://example.com", "chrome-extension://*"}
	middleware := CORSMiddleware(allowedOrigins)
	
	handler := middleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	tests := []struct {
		name           string
		origin         string
		method         string
		expectedStatus int
		expectCORS     bool
	}{
		{"allowed origin", "https://example.com", "GET", http.StatusOK, true},
		{"chrome extension", "chrome-extension://abc123", "GET", http.StatusOK, true},
		{"disallowed origin", "https://malicious.com", "GET", http.StatusOK, false},
		{"options request", "https://example.com", "OPTIONS", http.StatusOK, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, "/test", nil)
			req.Header.Set("Origin", tt.origin)
			rr := httptest.NewRecorder()

			handler.ServeHTTP(rr, req)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			corsHeader := rr.Header().Get("Access-Control-Allow-Origin")
			if tt.expectCORS && corsHeader == "" {
				t.Error("Expected CORS header to be set")
			}
		})
	}
}

func TestSecurityHeadersMiddleware(t *testing.T) {
	handler := SecurityHeadersMiddleware(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	handler.ServeHTTP(rr, req)

	expectedHeaders := map[string]string{
		"X-Content-Type-Options":   "nosniff",
		"X-Frame-Options":          "DENY",
		"X-XSS-Protection":         "1; mode=block",
		"Referrer-Policy":          "strict-origin-when-cross-origin",
		"Content-Security-Policy":  "default-src 'self'",
	}

	for header, expectedValue := range expectedHeaders {
		actualValue := rr.Header().Get(header)
		if actualValue != expectedValue {
			t.Errorf("Expected header %s to be %s, got %s", header, expectedValue, actualValue)
		}
	}
}

func TestIsOriginAllowed(t *testing.T) {
	allowedOrigins := []string{"https://example.com", "chrome-extension://*", "*.subdomain.com"}

	tests := []struct {
		name     string
		origin   string
		expected bool
	}{
		{"exact match", "https://example.com", true},
		{"chrome extension", "chrome-extension://abc123", true},
		{"subdomain wildcard", "https://test.subdomain.com", true},
		{"subdomain exact", "https://subdomain.com", false}, // This should be false since it's not in the allowed list
		{"disallowed origin", "https://malicious.com", false},
		{"empty origin", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isOriginAllowed(tt.origin, allowedOrigins)
			if result != tt.expected {
				t.Errorf("isOriginAllowed(%q) = %v, expected %v", tt.origin, result, tt.expected)
			}
		})
	}
}

func TestGenerateRequestID(t *testing.T) {
	id1 := generateRequestID()
	id2 := generateRequestID()

	if id1 == "" {
		t.Error("Expected non-empty request ID")
	}

	if id1 == id2 {
		t.Error("Expected unique request IDs")
	}

	if len(id1) != 16 { // 8 bytes = 16 hex characters
		t.Errorf("Expected request ID length of 16, got %d", len(id1))
	}
}

func TestSecureCompare(t *testing.T) {
	tests := []struct {
		name     string
		a        string
		b        string
		expected bool
	}{
		{"equal strings", "hello", "hello", true},
		{"different strings", "hello", "world", false},
		{"different lengths", "hello", "hello world", false},
		{"empty strings", "", "", true},
		{"one empty", "hello", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := secureCompare(tt.a, tt.b)
			if result != tt.expected {
				t.Errorf("secureCompare(%q, %q) = %v, expected %v", tt.a, tt.b, result, tt.expected)
			}
		})
	}
}
