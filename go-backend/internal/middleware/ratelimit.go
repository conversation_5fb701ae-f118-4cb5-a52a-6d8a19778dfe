package middleware

import (
	"net/http"
	"sync"
	"time"
)

// RateLimiter implements a simple token bucket rate limiter
type RateLimiter struct {
	mu       sync.RWMutex
	visitors map[string]*visitor
	rate     int           // requests per minute
	burst    int           // burst capacity
	cleanup  time.Duration // cleanup interval
}

// visitor represents a client with their rate limit state
type visitor struct {
	tokens   int
	lastSeen time.Time
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(rate, burst int) *RateLimiter {
	rl := &RateLimiter{
		visitors: make(map[string]*visitor),
		rate:     rate,
		burst:    burst,
		cleanup:  time.Minute * 5, // cleanup every 5 minutes
	}
	
	// Start cleanup goroutine
	go rl.cleanupVisitors()
	
	return rl
}

// RateLimitMiddleware creates a rate limiting middleware
func RateLimitMiddleware(rl *RateLimiter) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Get client identifier (IP address)
			clientIP := getClientIP(r)
			
			if !rl.allow(clientIP) {
				w.Header().Set("Retry-After", "60") // Suggest retry after 60 seconds
				http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
				return
			}
			
			next.ServeHTTP(w, r)
		})
	}
}

// allow checks if a request from the given client should be allowed
func (rl *RateLimiter) allow(clientIP string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	now := time.Now()
	
	// Get or create visitor
	v, exists := rl.visitors[clientIP]
	if !exists {
		v = &visitor{
			tokens:   rl.burst,
			lastSeen: now,
		}
		rl.visitors[clientIP] = v
	}
	
	// Calculate tokens to add based on time elapsed
	elapsed := now.Sub(v.lastSeen)
	tokensToAdd := int(elapsed.Minutes() * float64(rl.rate))
	
	// Add tokens, but don't exceed burst capacity
	v.tokens += tokensToAdd
	if v.tokens > rl.burst {
		v.tokens = rl.burst
	}
	
	v.lastSeen = now
	
	// Check if request can be allowed
	if v.tokens > 0 {
		v.tokens--
		return true
	}
	
	return false
}

// cleanupVisitors removes old visitor entries to prevent memory leaks
func (rl *RateLimiter) cleanupVisitors() {
	ticker := time.NewTicker(rl.cleanup)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			rl.mu.Lock()
			cutoff := time.Now().Add(-rl.cleanup * 2) // Remove visitors not seen for 10 minutes
			
			for ip, visitor := range rl.visitors {
				if visitor.lastSeen.Before(cutoff) {
					delete(rl.visitors, ip)
				}
			}
			rl.mu.Unlock()
		}
	}
}

// getClientIP extracts the client IP address from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header (for proxies)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// Take the first IP in the list
		if idx := len(xff); idx > 0 {
			if commaIdx := 0; commaIdx < idx {
				for i, char := range xff {
					if char == ',' {
						commaIdx = i
						break
					}
				}
				if commaIdx > 0 {
					return xff[:commaIdx]
				}
			}
			return xff
		}
	}
	
	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	
	// Fall back to RemoteAddr
	return r.RemoteAddr
}

// GetStats returns current rate limiter statistics
func (rl *RateLimiter) GetStats() map[string]interface{} {
	rl.mu.RLock()
	defer rl.mu.RUnlock()
	
	return map[string]interface{}{
		"active_visitors": len(rl.visitors),
		"rate_per_minute": rl.rate,
		"burst_capacity":  rl.burst,
	}
}

// Reset clears all visitor data (useful for testing)
func (rl *RateLimiter) Reset() {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	rl.visitors = make(map[string]*visitor)
}

// SetRate updates the rate limit (requests per minute)
func (rl *RateLimiter) SetRate(rate int) {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	rl.rate = rate
}

// SetBurst updates the burst capacity
func (rl *RateLimiter) SetBurst(burst int) {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	rl.burst = burst
}

// IPWhitelistMiddleware allows certain IPs to bypass rate limiting
func IPWhitelistMiddleware(whitelist []string) func(http.Handler) http.Handler {
	whitelistMap := make(map[string]bool)
	for _, ip := range whitelist {
		whitelistMap[ip] = true
	}
	
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			clientIP := getClientIP(r)
			
			// Check if IP is whitelisted
			if whitelistMap[clientIP] {
				// Skip rate limiting for whitelisted IPs
				next.ServeHTTP(w, r)
				return
			}
			
			next.ServeHTTP(w, r)
		})
	}
}
