package handlers

import (
	"github.com/LosEcher/lot2extension/go-backend/internal/config"
	"github.com/LosEcher/lot2extension/go-backend/internal/database"
	"github.com/gorilla/mux"
)

// RegisterBasicHandlers registers basic handlers like health checks.
func RegisterBasicHandlers(r *mux.Router) {
	// Basic handlers can be added here, e.g., r.<PERSON>("/health", HealthCheckHandler)
}

// RegisterAPIHandlers registers all the API handlers.
func RegisterAPIHandlers(r *mux.Router, db *database.DB, cfg *config.Config) {
	api := r.PathPrefix("/api").Subrouter()

	// Only register database-dependent handlers if database is available
	if db != nil {
		// Todos
		api.HandleFunc("/todos", getTodosHandler(db)).Methods("GET", "OPTIONS")
		api.HandleFunc("/todos/add", addTodoHandler(db)).Methods("POST", "OPTIONS")

		// Categories
		api.HandleFunc("/categories", getCategoriesHandler(db)).Methods("GET", "OPTIONS")
		api.HandleFunc("/categories/add", addCategoryHandler(db)).Methods("POST", "OPTIONS")
		api.HandleFunc("/categories/update", updateCategoryHandler(db)).Methods("POST", "OPTIONS")
		api.HandleFunc("/categories/delete", deleteCategoryHandler(db)).Methods("POST", "OPTIONS")

		// Tags
		api.HandleFunc("/tags", getTagsHandler(db)).Methods("GET", "OPTIONS")
		api.HandleFunc("/tags/add", addTagHandler(db)).Methods("POST", "OPTIONS")
		api.HandleFunc("/tags/update", updateTagHandler(db)).Methods("POST", "OPTIONS")
		api.HandleFunc("/tags/delete", deleteTagHandler(db)).Methods("POST", "OPTIONS")
	}

	// Axure Path Management (file serving works without database)
	RegisterAxurePathHandlers(api, db, cfg)
}