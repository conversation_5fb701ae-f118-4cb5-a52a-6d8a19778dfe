package handlers

import (
	"github.com/LosEcher/lot2extension/go-backend/internal/database"
	"github.com/gorilla/mux"
)

// RegisterBasicHandlers registers basic handlers like health checks.
func RegisterBasicHandlers(r *mux.Router) {
	// Basic handlers can be added here, e.g., r.<PERSON>("/health", HealthCheckHandler)
}

// RegisterAPIHandlers registers all the API handlers.
func RegisterAPIHandlers(r *mux.Router, db *database.DB) {
	api := r.PathPrefix("/api").Subrouter()

	// Todos
	api.HandleFunc("/todos", getTodosHandler(db)).Methods("GET", "OPTIONS")
	api.HandleFunc("/todos/add", addTodoHandler(db)).Methods("POST", "OPTIONS")

	// Categories
	api.HandleFunc("/categories", getCategoriesHandler(db)).Methods("GET", "OPTIONS")
	api.HandleFunc("/categories/add", addCategoryHandler(db)).Methods("POST", "OPTIONS")
	api.HandleFunc("/categories/update", updateCategoryHandler(db)).Methods("POST", "OPTIONS")
	api.HandleFunc("/categories/delete", deleteCategoryHandler(db)).Methods("POST", "OPTIONS")

	// Tags
	api.HandleFunc("/tags", getTagsHandler(db)).Methods("GET", "OPTIONS")
	api.HandleFunc("/tags/add", addTagHandler(db)).Methods("POST", "OPTIONS")
	api.HandleFunc("/tags/update", updateTagHandler(db)).Methods("POST", "OPTIONS")
	api.HandleFunc("/tags/delete", deleteTagHandler(db)).Methods("POST", "OPTIONS")

	// Axure Path Management
	RegisterAxurePathHandlers(api, db)
}

// RegisterAxurePathHandlers registers the handlers for Axure path management.
func RegisterAxurePathHandlers(r *mux.Router, db *database.DB) {
	axureAPI := r.PathPrefix("/axure").Subrouter()
	axureAPI.HandleFunc("/paths", CreateAxurePathHandler(db)).Methods("POST", "OPTIONS")
	axureAPI.HandleFunc("/paths", GetAxurePathsHandler(db)).Methods("GET", "OPTIONS")
	axureAPI.HandleFunc("/paths/{id:[0-9]+}", GetAxurePathHandler(db)).Methods("GET", "OPTIONS")
	axureAPI.HandleFunc("/paths/{id:[0-9]+}", UpdateAxurePathHandler(db)).Methods("PUT", "OPTIONS")
	axureAPI.HandleFunc("/paths/{id:[0-9]+}", DeleteAxurePathHandler(db)).Methods("DELETE", "OPTIONS")
	// Register the file listing and serving endpoints
	axureAPI.HandleFunc("/list-files", ListFilesInDirHandler).Methods("GET")
	axureAPI.HandleFunc("/serve-file", ServeFileHandler).Methods("GET")
}