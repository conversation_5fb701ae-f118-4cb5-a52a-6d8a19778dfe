package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/LosEcher/lot2extension/go-backend/internal/config"
	"github.com/LosEcher/lot2extension/go-backend/internal/database"
	"github.com/LosEcher/lot2extension/go-backend/internal/models"
	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
)

// CreateAxurePathHandler handles the creation of a new Axure path record.
func CreateAxurePathHandler(db *database.DB, cfg *config.Config) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var axurePath models.AxurePath
		if err := json.NewDecoder(r.Body).Decode(&axurePath); err != nil {
			RespondWithError(w, http.StatusBadRequest, "Invalid request payload")
			return
		}

		// Validate the input
		validate := validator.New()
		if err := validate.Struct(axurePath); err != nil {
			RespondWithError(w, http.StatusBadRequest, "Validation failed: "+err.Error())
			return
		}

		// Convert relative path to absolute path and validate
		var fullPath string
		if filepath.IsAbs(axurePath.Path) {
			fullPath = axurePath.Path
		} else {
			fullPath = filepath.Join(cfg.Security.AllowedBaseDir, axurePath.Path)
		}

		// Clean and validate the path
		cleanPath := filepath.Clean(fullPath)
		absPath, err := filepath.Abs(cleanPath)
		if err != nil {
			RespondWithError(w, http.StatusBadRequest, "Invalid path")
			return
		}

		// Security check: ensure path is within allowed base directory
		if cfg.Security.AllowedBaseDir != "" && !strings.HasPrefix(absPath, cfg.Security.AllowedBaseDir) {
			RespondWithError(w, http.StatusForbidden, "Path outside allowed directory")
			return
		}

		// Check if directory exists
		if _, err := os.Stat(absPath); os.IsNotExist(err) {
			RespondWithError(w, http.StatusBadRequest, "Directory does not exist: "+absPath)
			return
		}

		// Store the original relative path in database for display purposes
		// but validate against the absolute path
		query := "INSERT INTO axure_paths (name, path) VALUES (?, ?)"
		result, err := db.MySQL.Exec(query, axurePath.Name, axurePath.Path)
		if err != nil {
			log.Printf("Error creating axure path: %v", err)
			RespondWithError(w, http.StatusInternalServerError, "Failed to create Axure path")
			return
		}

		id, err := result.LastInsertId()
		if err != nil {
			log.Printf("Error getting last insert ID: %v", err)
			RespondWithError(w, http.StatusInternalServerError, "Failed to retrieve Axure path ID")
			return
		}

		axurePath.ID = int(id)

		RespondWithJSON(w, http.StatusCreated, axurePath)
	}
}

// GetAxurePathsHandler handles fetching all Axure path records.
func GetAxurePathsHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		query := "SELECT id, name, path, created_at, updated_at FROM axure_paths"
		rows, err := db.MySQL.Query(query)
		if err != nil {
			log.Printf("Error fetching axure paths: %v", err)
			RespondWithError(w, http.StatusInternalServerError, "Failed to fetch Axure paths")
			return
		}
		defer rows.Close()

		paths := []models.AxurePath{}
		for rows.Next() {
			var path models.AxurePath
			if err := rows.Scan(&path.ID, &path.Name, &path.Path, &path.CreatedAt, &path.UpdatedAt); err != nil {
				log.Printf("Error scanning axure path: %v", err)
				continue // Or handle more gracefully
			}
			paths = append(paths, path)
		}

		RespondWithJSON(w, http.StatusOK, paths)
	}
}

// GetAxurePathHandler handles fetching a single Axure path by its ID.
func GetAxurePathHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		vars := mux.Vars(r)
		id, ok := vars["id"]
		if !ok {
			RespondWithError(w, http.StatusBadRequest, "Missing path ID")
			return
		}

		var path models.AxurePath
		query := "SELECT id, name, path, created_at, updated_at FROM axure_paths WHERE id = ?"
		err := db.MySQL.QueryRow(query, id).Scan(&path.ID, &path.Name, &path.Path, &path.CreatedAt, &path.UpdatedAt)
		if err != nil {
			if err == sql.ErrNoRows {
				RespondWithError(w, http.StatusNotFound, "Axure path not found")
			} else {
				log.Printf("Error fetching axure path: %v", err)
				RespondWithError(w, http.StatusInternalServerError, "Failed to fetch Axure path")
			}
			return
		}

		RespondWithJSON(w, http.StatusOK, path)
	}
}

// UpdateAxurePathHandler handles updating an existing Axure path record.
func UpdateAxurePathHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		vars := mux.Vars(r)
		id, ok := vars["id"]
		if !ok {
			RespondWithError(w, http.StatusBadRequest, "Missing path ID")
			return
		}

		var axurePath models.AxurePath
		if err := json.NewDecoder(r.Body).Decode(&axurePath); err != nil {
			RespondWithError(w, http.StatusBadRequest, "Invalid request payload")
			return
		}

		validate := validator.New()
		if err := validate.Struct(axurePath); err != nil {
			RespondWithError(w, http.StatusBadRequest, "Validation failed: "+err.Error())
			return
		}

		query := "UPDATE axure_paths SET name = ?, path = ? WHERE id = ?"
		_, err := db.MySQL.Exec(query, axurePath.Name, axurePath.Path, id)
		if err != nil {
			log.Printf("Error updating axure path: %v", err)
			RespondWithError(w, http.StatusInternalServerError, "Failed to update Axure path")
			return
		}

		RespondWithJSON(w, http.StatusOK, models.Response{Message: "Axure path updated successfully"})
	}
}

// DeleteAxurePathHandler handles deleting an Axure path record.
func DeleteAxurePathHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		vars := mux.Vars(r)
		id, ok := vars["id"]
		if !ok {
			RespondWithError(w, http.StatusBadRequest, "Missing path ID")
			return
		}

		query := "DELETE FROM axure_paths WHERE id = ?"
		_, err := db.MySQL.Exec(query, id)
		if err != nil {
			log.Printf("Error deleting axure path: %v", err)
			RespondWithError(w, http.StatusInternalServerError, "Failed to delete Axure path")
			return
		}

		RespondWithJSON(w, http.StatusNoContent, nil)
	}
}

// ListFilesInDirHandler lists files in a given directory
func ListFilesInDirHandler(cfg *config.Config) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
	dir := r.URL.Query().Get("path")
	if dir == "" {
		RespondWithError(w, http.StatusBadRequest, "Missing path parameter")
		return
	}

	// Convert relative path to absolute path
	var fullPath string
	if filepath.IsAbs(dir) {
		fullPath = dir
	} else {
		fullPath = filepath.Join(cfg.Security.AllowedBaseDir, dir)
	}

	// Add path validation and sanitization
	absDir, err := filepath.Abs(filepath.Clean(fullPath))
	if err != nil {
		RespondWithError(w, http.StatusBadRequest, "Invalid path")
		return
	}

	// Add security check for allowed base directory
	allowedBaseDir := cfg.Security.AllowedBaseDir
	if allowedBaseDir == "" {
		log.Printf("ListFilesInDirHandler: allowed base directory not configured")
		RespondWithError(w, http.StatusInternalServerError, "Server configuration error")
		return
	}
	if !strings.HasPrefix(absDir, allowedBaseDir) {
		log.Printf("ListFilesInDirHandler: access denied - path: %s (not within allowed dir: %s)", absDir, allowedBaseDir)
		RespondWithError(w, http.StatusForbidden, "Access denied")
		return
	}

	files, err := os.ReadDir(absDir)
	if err != nil {
		RespondWithError(w, http.StatusInternalServerError, "Failed to read directory: "+err.Error())
		return
	}
	var fileList []map[string]interface{}
	for _, f := range files {
		fileList = append(fileList, map[string]interface{}{
			"name": f.Name(),
			"isDir": f.IsDir(),
		})
	}
	RespondWithJSON(w, http.StatusOK, map[string]interface{}{
		"path": absDir,
		"files": fileList,
	})
}
}

func ServeFileHandler(cfg *config.Config) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
    file := r.URL.Query().Get("file")
    basePath := r.URL.Query().Get("basePath")
    if file == "" || basePath == "" {
        log.Printf("ServeFileHandler: missing parameters - file: %s, basePath: %s", file, basePath)
        RespondWithError(w, http.StatusBadRequest, "Missing required parameters")
        return
    }

    // Convert relative path to absolute path
    var fullBasePath string
    if filepath.IsAbs(basePath) {
        fullBasePath = basePath
    } else {
        fullBasePath = filepath.Join(cfg.Security.AllowedBaseDir, basePath)
    }

    // Enhanced path security checks
    cleanBasePath := filepath.Clean(fullBasePath)
    absBasePath, err := filepath.Abs(cleanBasePath)
    if err != nil {
        log.Printf("ServeFileHandler: invalid base path - path: %s, error: %v", basePath, err)
        RespondWithError(w, http.StatusBadRequest, "Invalid base path")
        return
    }

    // Check against allowed directory
    allowedBaseDir := cfg.Security.AllowedBaseDir
    if allowedBaseDir == "" {
        log.Printf("ServeFileHandler: allowed base directory not configured")
        RespondWithError(w, http.StatusInternalServerError, "Server configuration error")
        return
    }
    if !strings.HasPrefix(absBasePath, allowedBaseDir) {
        log.Printf("ServeFileHandler: access denied - path: %s (not within allowed dir: %s)", absBasePath, allowedBaseDir)
        RespondWithError(w, http.StatusForbidden, "Access denied")
        return
    }

    cleanFile := filepath.Clean(file)
    absPath := filepath.Join(absBasePath, cleanFile)
    
    // Final path traversal check
    if !strings.HasPrefix(absPath, absBasePath) {
        log.Printf("ServeFileHandler: path traversal attempt detected - base: %s, full: %s", absBasePath, absPath)
        RespondWithError(w, http.StatusForbidden, "Access denied")
        return
    }

    // Check file exists and is regular file
    if fileInfo, err := os.Stat(absPath); err != nil {
        if os.IsNotExist(err) {
            log.Printf("ServeFileHandler: file not found - path: %s", absPath)
            RespondWithError(w, http.StatusNotFound, "File not found")
        } else {
            log.Printf("ServeFileHandler: error accessing file - path: %s, error: %v", absPath, err)
            RespondWithError(w, http.StatusInternalServerError, "Error accessing file")
        }
        return
    } else if !fileInfo.Mode().IsRegular() {
        log.Printf("ServeFileHandler: not a regular file - path: %s, mode: %v", absPath, fileInfo.Mode())
        RespondWithError(w, http.StatusBadRequest, "Not a regular file")
        return
    }

    log.Printf("ServeFileHandler: serving file successfully - path: %s", absPath)

    // Set proper content headers based on file extension
    contentType := mime.TypeByExtension(filepath.Ext(absPath))
    if contentType == "" {
        contentType = "application/octet-stream"
    }
    w.Header().Set("Content-Type", contentType)

    // For HTML files, set inline disposition; for others, allow browser to decide
    if strings.HasSuffix(strings.ToLower(absPath), ".html") || strings.HasSuffix(strings.ToLower(absPath), ".htm") {
        w.Header().Set("Content-Disposition", "inline")
    } else {
        w.Header().Set("Content-Disposition", fmt.Sprintf("inline; filename=\"%s\"", filepath.Base(absPath)))
    }

    http.ServeFile(w, r, absPath)
	}
}

// RespondWithError sends a JSON error response.
func RespondWithError(w http.ResponseWriter, code int, message string) {
	RespondWithJSON(w, code, map[string]string{"error": message})
}

// RespondWithJSON sends a JSON response.
func RespondWithJSON(w http.ResponseWriter, code int, payload interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	if payload != nil {
		json.NewEncoder(w).Encode(payload)
	}
}

// RegisterAxurePathHandlers registers the handlers for Axure path management.
// This is the primary implementation, moved from api.go to avoid duplication
func RegisterAxurePathHandlers(r *mux.Router, db *database.DB, cfg *config.Config) {
	axureAPI := r.PathPrefix("/axure").Subrouter()

	// Only register database-dependent handlers if database is available
	if db != nil {
		axureAPI.HandleFunc("/paths", CreateAxurePathHandler(db, cfg)).Methods("POST", "OPTIONS")
		axureAPI.HandleFunc("/paths", GetAxurePathsHandler(db)).Methods("GET", "OPTIONS")
		axureAPI.HandleFunc("/paths/{id:[0-9]+}", GetAxurePathHandler(db)).Methods("GET", "OPTIONS")
		axureAPI.HandleFunc("/paths/{id:[0-9]+}", UpdateAxurePathHandler(db)).Methods("PUT", "OPTIONS")
		axureAPI.HandleFunc("/paths/{id:[0-9]+}", DeleteAxurePathHandler(db)).Methods("DELETE", "OPTIONS")
	}

	// File serving endpoints work without database
	axureAPI.HandleFunc("/list-files", ListFilesInDirHandler(cfg)).Methods("GET", "HEAD", "OPTIONS")
	axureAPI.HandleFunc("/serve-file", ServeFileHandler(cfg)).Methods("GET", "HEAD", "OPTIONS")
}
