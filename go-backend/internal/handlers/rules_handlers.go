package handlers

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/LosEcher/lot2extension/go-backend/internal/database"
	"github.com/LosEcher/lot2extension/go-backend/internal/errors"
	"github.com/LosEcher/lot2extension/go-backend/internal/models"
	"github.com/LosEcher/lot2extension/go-backend/internal/services"
	"github.com/LosEcher/lot2extension/go-backend/internal/validation"
)

// getRulesHandler handles GET /api/rules
func getRulesHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		rulesService := services.NewRulesService(db.MySQL)

		rules, err := rulesService.GetAllRules()
		if err != nil {
			log.Printf("Error fetching rules: %v", err)
			RespondWithError(w, http.StatusInternalServerError, "Failed to fetch rules")
			return
		}

		RespondWithJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"rules":   rules,
			"count":   len(rules),
		})
	}
}

// addRule<PERSON>andler handles POST /api/rules/add
func addRuleHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		defer errors.HandlePanic(w, r)

		var rule models.Rule
		if err := json.NewDecoder(r.Body).Decode(&rule); err != nil {
			errors.SendError(w, errors.ErrInvalidJSON)
			return
		}

		// Sanitize input
		rule.URL = validation.SanitizeString(rule.URL)
		rule.Description = validation.SanitizeString(rule.Description)
		rule.CreatedBy = validation.SanitizeString(rule.CreatedBy)

		// Validate input
		if validationErrors := validation.ValidateRule(rule); validationErrors.HasErrors() {
			errors.SendValidationError(w, validationErrors)
			return
		}

		rulesService := services.NewRulesService(db.MySQL)

		if err := rulesService.AddRule(rule); err != nil {
			log.Printf("Error adding rule: %v", err)
			if err.Error() == "rule with URL "+rule.URL+" already exists" {
				errors.SendConflict(w, err.Error())
			} else {
				errors.SendInternalError(w, "Failed to add rule")
			}
			return
		}

		RespondWithJSON(w, http.StatusCreated, map[string]interface{}{
			"success": true,
			"message": "Rule added successfully",
		})
	}
}

// updateRuleHandler handles PUT /api/rules/update
func updateRuleHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		defer errors.HandlePanic(w, r)

		var rule models.Rule
		if err := json.NewDecoder(r.Body).Decode(&rule); err != nil {
			errors.SendError(w, errors.ErrInvalidJSON)
			return
		}

		// Sanitize input
		rule.URL = validation.SanitizeString(rule.URL)
		rule.Description = validation.SanitizeString(rule.Description)
		rule.CreatedBy = validation.SanitizeString(rule.CreatedBy)

		// Validate input
		if validationErrors := validation.ValidateRule(rule); validationErrors.HasErrors() {
			errors.SendValidationError(w, validationErrors)
			return
		}

		rulesService := services.NewRulesService(db.MySQL)

		if err := rulesService.UpdateRule(rule); err != nil {
			log.Printf("Error updating rule: %v", err)
			if err.Error() == "rule with URL "+rule.URL+" not found" {
				errors.SendNotFound(w, err.Error())
			} else {
				errors.SendInternalError(w, "Failed to update rule")
			}
			return
		}

		RespondWithJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"message": "Rule updated successfully",
		})
	}
}

// deleteRuleHandler handles DELETE /api/rules/delete
func deleteRuleHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		defer errors.HandlePanic(w, r)

		url := r.URL.Query().Get("url")
		if url == "" {
			errors.SendBadRequest(w, "URL parameter is required")
			return
		}

		// Sanitize input
		url = validation.SanitizeString(url)

		// Validate URL format
		if validationErrors := validation.ValidateRequiredString(url, "url"); validationErrors.HasErrors() {
			errors.SendValidationError(w, validationErrors)
			return
		}

		rulesService := services.NewRulesService(db.MySQL)

		if err := rulesService.DeleteRule(url); err != nil {
			log.Printf("Error deleting rule: %v", err)
			if err.Error() == "no rule found with URL "+url {
				errors.SendNotFound(w, err.Error())
			} else {
				errors.SendInternalError(w, "Failed to delete rule")
			}
			return
		}

		RespondWithJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"message": "Rule deleted successfully",
		})
	}
}
