package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/mux"
)

// FileService manages Axure file serving and access logging
type FileService struct {
	sync.RWMutex
	servedFiles map[string]string   // key: endpoint path, value: file path
	accessLogs  map[string][]string // key: endpoint path, value: list of access timestamps
}

// FileInfo contains metadata about a served file
type FileInfo struct {
	Endpoint    string   `json:"endpoint"`
	FilePath    string   `json:"file_path"`
	AccessCount int      `json:"access_count"`
	LastAccess  string   `json:"last_access,omitempty"`
	AccessLogs  []string `json:"access_logs,omitempty"`
}

// AddFileRequest represents a request to add a new file to the service
type AddFileRequest struct {
	Path     string `json:"path"`
	Endpoint string `json:"endpoint,omitempty"` // 可选的自定义端点
}

// RemoveFileRequest represents a request to remove a file from the service
type RemoveFileRequest struct {
	Endpoint string `json:"endpoint"`
}

// NewFileService creates and initializes a new FileService instance
func NewFileService() *FileService {
	return &FileService{
		servedFiles: make(map[string]string),
		accessLogs:  make(map[string][]string),
	}
}

// RegisterAxureFileServiceHandlers registers HTTP handlers for Axure file operations
func RegisterAxureFileServiceHandlers(router *mux.Router, fs *FileService) error {
	axureRouter := router.PathPrefix("/axure").Subrouter()
	axureRouter.HandleFunc("/add", fs.addFileHandler).Methods("POST", "OPTIONS")
	axureRouter.HandleFunc("/remove", fs.removeFileHandler).Methods("POST", "OPTIONS")
	axureRouter.HandleFunc("/list", fs.listFilesHandler).Methods("GET", "OPTIONS")
	axureRouter.HandleFunc("/info", fs.getFileInfoHandler).Methods("GET", "OPTIONS")
	axureRouter.PathPrefix("/files/").HandlerFunc(fs.serveFileHandler).Methods("GET", "OPTIONS")
	return nil
}

// addFileHandler handles requests to add a new file to the service
func (fs *FileService) addFileHandler(w http.ResponseWriter, r *http.Request) {
	setCorsHeaders(w)
	
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}
	
	if r.Method != http.MethodPost {
		sendErrorResponse(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Limit request body size to prevent DoS
	r.Body = http.MaxBytesReader(w, r.Body, 10<<20) // 10MB limit

	var req AddFileRequest
	contentType := r.Header.Get("Content-Type")
	if strings.HasPrefix(contentType, "application/json") {
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			sendErrorResponse(w, "Invalid JSON payload", http.StatusBadRequest)
			return
		}
	} else {
		// Handle form data
		if err := r.ParseForm(); err != nil {
			sendErrorResponse(w, "Invalid form data", http.StatusBadRequest)
			return
		}
		req.Path = r.FormValue("path")
		req.Endpoint = r.FormValue("endpoint")
	}

	// Validate path
	req.Path = strings.TrimSpace(req.Path)
	if req.Path == "" {
		sendErrorResponse(w, "Path parameter is required", http.StatusBadRequest)
		return
	}

	// Sanitize and validate the file path
	abspath, err := filepath.Abs(filepath.Clean(req.Path))
	if err != nil {
		sendErrorResponse(w, "Invalid path", http.StatusBadRequest)
		return
	}

	// Prevent directory traversal - THIS IS A CRITICAL SECURITY CHECK
	// You MUST configure this to a safe, known base directory.
	// For example: /var/www/axure_projects
	// LEAVING THIS UNCONFIGURED IS A SECURITY RISK.
	allowedBaseDir := "/path/to/allowed/directory" // TODO: Configure this properly!
	if !strings.HasPrefix(abspath, allowedBaseDir) {
		sendErrorResponse(w, "Access denied", http.StatusForbidden)
		return
	}

	// Check if file exists and is accessible
	fileInfo, err := os.Stat(abspath)
	if err != nil {
		if os.IsNotExist(err) {
			sendErrorResponse(w, "File does not exist", http.StatusNotFound)
		} else if os.IsPermission(err) {
			sendErrorResponse(w, "Permission denied", http.StatusForbidden)
		} else {
			sendErrorResponse(w, "Error accessing file", http.StatusInternalServerError)
		}
		return
	}

	// Check if it's a regular file
	if !fileInfo.Mode().IsRegular() {
		sendErrorResponse(w, "Not a regular file", http.StatusBadRequest)
		return
	}

	// Generate or validate endpoint
	var endpoint string
	if req.Endpoint != "" {
		endpoint = "/axure/files/" + strings.TrimPrefix(req.Endpoint, "/")
		// Validate endpoint format
		if !isValidEndpoint(endpoint) {
			sendErrorResponse(w, "Invalid endpoint format", http.StatusBadRequest)
			return
		}
	} else {
		endpoint = "/axure/files/" + filepath.Base(abspath)
	}

	// Check if endpoint already exists
	fs.RLock()
	_, exists := fs.servedFiles[endpoint]
	fs.RUnlock()

	if exists {
		sendErrorResponse(w, "Endpoint already exists", http.StatusConflict)
		return
	}

	// Add file to service
	fs.Lock()
	fs.servedFiles[endpoint] = abspath
	fs.accessLogs[endpoint] = []string{}
	fs.Unlock()

	log.Printf("File added to service: %s -> %s", endpoint, abspath)

	// Return success response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	response := map[string]interface{}{
		"success":  true,
		"message":  "File added successfully",
		"endpoint": endpoint,
		"path":     abspath,
	}
	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Error encoding response: %v", err)
	}
}

// removeFileHandler handles requests to remove a file from the service
func (fs *FileService) removeFileHandler(w http.ResponseWriter, r *http.Request) {
	setCorsHeaders(w)
	
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}
	
	if r.Method != http.MethodPost && r.Method != http.MethodDelete {
		sendErrorResponse(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req RemoveFileRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		// 如果JSON解析失败，尝试从表单参数获取
		req.Endpoint = r.FormValue("endpoint")
	}

	fs.Lock()
	delete(fs.servedFiles, req.Endpoint)
	delete(fs.accessLogs, req.Endpoint)
	fs.Unlock()

	log.Printf("File removed from service: %s", req.Endpoint)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{"success": true, "message": "File removed successfully"})
}

// listFilesHandler handles requests to list all served files
func (fs *FileService) listFilesHandler(w http.ResponseWriter, r *http.Request) {
	setCorsHeaders(w)
	fs.RLock()
	defer fs.RUnlock()

	files := make([]FileInfo, 0, len(fs.servedFiles))
	for endpoint, path := range fs.servedFiles {
		logs := fs.accessLogs[endpoint]
		var lastAccess string
		if len(logs) > 0 {
			lastAccess = logs[len(logs)-1]
		}
		files = append(files, FileInfo{
			Endpoint:    endpoint,
			FilePath:    path,
			AccessCount: len(logs),
			LastAccess:  lastAccess,
		})
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(files)
}

// getFileInfoHandler handles requests for information about a specific file
func (fs *FileService) getFileInfoHandler(w http.ResponseWriter, r *http.Request) {
	setCorsHeaders(w)
	endpoint := r.URL.Query().Get("endpoint")
	if endpoint == "" {
		sendErrorResponse(w, "Endpoint parameter is required", http.StatusBadRequest)
		return
	}

	fs.RLock()
	defer fs.RUnlock()

	path, ok := fs.servedFiles[endpoint]
	if !ok {
		sendErrorResponse(w, "File not found", http.StatusNotFound)
		return
	}

	logs := fs.accessLogs[endpoint]
	var lastAccess string
	if len(logs) > 0 {
		lastAccess = logs[len(logs)-1]
	}

	info := FileInfo{
		Endpoint:    endpoint,
		FilePath:    path,
		AccessCount: len(logs),
		LastAccess:  lastAccess,
		AccessLogs:  logs,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(info)
}

// serveFileHandler serves the actual Axure file content
func (fs *FileService) serveFileHandler(w http.ResponseWriter, r *http.Request) {
	setCorsHeaders(w)
	endpoint := r.URL.Path

	fs.RLock()
	path, ok := fs.servedFiles[endpoint]
	fs.RUnlock()

	if !ok {
		http.NotFound(w, r)
		return
	}

	fs.Lock()
	fs.accessLogs[endpoint] = append(fs.accessLogs[endpoint], time.Now().Format(time.RFC3339))
	fs.Unlock()

	// Serve the file
	http.ServeFile(w, r, path)
}

func setCorsHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
	w.Header().Set("Access-Control-Allow-Headers", "Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-KEY")
}

func sendErrorResponse(w http.ResponseWriter, message string, code int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	json.NewEncoder(w).Encode(map[string]string{"error": message})
}

func isValidEndpoint(endpoint string) bool {
	// Basic validation for endpoint format
	return true
}