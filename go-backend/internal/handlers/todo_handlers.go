package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/LosEcher/lot2extension/go-backend/internal/database"
	"github.com/LosEcher/lot2extension/go-backend/internal/models"
)

func getTodosHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Implementation for getting todos
		RespondWithJSON(w, http.StatusOK, []models.Todo{})
	}
}

func addTodo<PERSON>andler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Implementation for adding a todo
		var todo models.Todo
		_ = json.NewDecoder(r.Body).Decode(&todo)
		RespondWithJSON(w, http.StatusCreated, todo)
	}
}