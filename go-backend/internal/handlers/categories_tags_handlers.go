package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/LosEcher/lot2extension/go-backend/internal/database"
	"github.com/LosEcher/lot2extension/go-backend/internal/models"
)

// getCategoriesHandler handles GET /api/categories
func getCategoriesHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// TODO: Implement actual database query
		categories := []models.Category{
			{ID: 1, Name: "Work"},
			{ID: 2, Name: "Personal"},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success":    true,
			"categories": categories,
		})
	}
}

// addCategoryHandler handles POST /api/categories/add
func addCategoryHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var category models.Category
		if err := json.NewDecoder(r.Body).Decode(&category); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		// TODO: Implement actual database insert
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": "Category added successfully",
			"id":      3, // TODO: Return actual ID from database
		})
	}
}

// updateCategoryHandler handles PUT /api/categories/update
func updateCategoryHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var category models.Category
		if err := json.NewDecoder(r.Body).Decode(&category); err != nil {
			http.Error(w, "Invalid request body", http.StatusBadRequest)
			return
		}

		if category.ID == 0 {
			http.Error(w, "Category ID is required", http.StatusBadRequest)
			return
		}

		// TODO: Implement actual database update
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": "Category updated successfully",
		})
	}
}

// deleteCategoryHandler handles DELETE /api/categories/delete
func deleteCategoryHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		id := r.URL.Query().Get("id")
		if id == "" {
			http.Error(w, "ID parameter is required", http.StatusBadRequest)
			return
		}

		// TODO: Implement actual database delete
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"message": "Category deleted successfully",
		})
	}
}

// getTagsHandler handles GET /api/tags
func getTagsHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// TODO: Implement actual database query
		tags := []models.Tag{
			{ID: 1, Name: "important"},
			{ID: 2, Name: "urgent"},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success": true,
			"tags":    tags,
		})
	}
}

// addTagHandler handles POST /api/tags/add
func addTagHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Implementation for adding a tag
		var tag models.Tag
		_ = json.NewDecoder(r.Body).Decode(&tag)
		RespondWithJSON(w, http.StatusCreated, tag)
	}
}

// updateTagHandler handles POST /api/tags/update
func updateTagHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Implementation for updating a tag
		var tag models.Tag
		_ = json.NewDecoder(r.Body).Decode(&tag)
		RespondWithJSON(w, http.StatusOK, tag)
	}
}

// deleteTagHandler handles POST /api/tags/delete
func deleteTagHandler(db *database.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Implementation for deleting a tag
		var tag models.Tag
		_ = json.NewDecoder(r.Body).Decode(&tag)
		RespondWithJSON(w, http.StatusOK, tag)
	}
}
