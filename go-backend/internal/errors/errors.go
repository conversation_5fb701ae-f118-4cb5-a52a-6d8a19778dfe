package errors

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/LosEcher/lot2extension/go-backend/internal/validation"
)

// ErrorCode represents standardized error codes
type ErrorCode string

const (
	// Client errors (4xx)
	ErrCodeBadRequest     ErrorCode = "BAD_REQUEST"
	ErrCodeUnauthorized   ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden      ErrorCode = "FORBIDDEN"
	ErrCodeNotFound       ErrorCode = "NOT_FOUND"
	ErrCodeConflict       ErrorCode = "CONFLICT"
	ErrCodeValidation     ErrorCode = "VALIDATION_ERROR"
	ErrCodeTooManyReqs    ErrorCode = "TOO_MANY_REQUESTS"

	// Server errors (5xx)
	ErrCodeInternal       ErrorCode = "INTERNAL_ERROR"
	ErrCodeNotImplemented ErrorCode = "NOT_IMPLEMENTED"
	ErrCodeServiceUnavail ErrorCode = "SERVICE_UNAVAILABLE"
	ErrCodeTimeout        ErrorCode = "TIMEOUT"
)

// APIError represents a standardized API error response
type APIError struct {
	Success   bool                        `json:"success"`
	Error     ErrorDetails                `json:"error"`
	RequestID string                      `json:"request_id,omitempty"`
	Timestamp string                      `json:"timestamp"`
}

// ErrorDetails contains detailed error information
type ErrorDetails struct {
	Code       ErrorCode                   `json:"code"`
	Message    string                      `json:"message"`
	Details    string                      `json:"details,omitempty"`
	Validation []validation.ValidationError `json:"validation,omitempty"`
}

// NewAPIError creates a new API error
func NewAPIError(code ErrorCode, message string) *APIError {
	return &APIError{
		Success: false,
		Error: ErrorDetails{
			Code:    code,
			Message: message,
		},
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// NewValidationError creates a new validation error
func NewValidationError(message string, validationErrors validation.ValidationErrors) *APIError {
	return &APIError{
		Success: false,
		Error: ErrorDetails{
			Code:       ErrCodeValidation,
			Message:    message,
			Validation: validationErrors,
		},
		Timestamp: time.Now().UTC().Format(time.RFC3339),
	}
}

// WithDetails adds additional details to the error
func (e *APIError) WithDetails(details string) *APIError {
	e.Error.Details = details
	return e
}

// WithRequestID adds a request ID to the error
func (e *APIError) WithRequestID(requestID string) *APIError {
	e.RequestID = requestID
	return e
}

// GetHTTPStatus returns the appropriate HTTP status code for the error
func (e *APIError) GetHTTPStatus() int {
	switch e.Error.Code {
	case ErrCodeBadRequest, ErrCodeValidation:
		return http.StatusBadRequest
	case ErrCodeUnauthorized:
		return http.StatusUnauthorized
	case ErrCodeForbidden:
		return http.StatusForbidden
	case ErrCodeNotFound:
		return http.StatusNotFound
	case ErrCodeConflict:
		return http.StatusConflict
	case ErrCodeTooManyReqs:
		return http.StatusTooManyRequests
	case ErrCodeNotImplemented:
		return http.StatusNotImplemented
	case ErrCodeServiceUnavail:
		return http.StatusServiceUnavailable
	case ErrCodeTimeout:
		return http.StatusRequestTimeout
	case ErrCodeInternal:
		fallthrough
	default:
		return http.StatusInternalServerError
	}
}

// SendError sends a standardized error response
func SendError(w http.ResponseWriter, err *APIError) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(err.GetHTTPStatus())

	if encodeErr := json.NewEncoder(w).Encode(err); encodeErr != nil {
		log.Printf("Failed to encode error response: %v", encodeErr)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// SendValidationError sends a validation error response
func SendValidationError(w http.ResponseWriter, validationErrors validation.ValidationErrors) {
	err := NewValidationError("Validation failed", validationErrors)
	SendError(w, err)
}

// SendBadRequest sends a bad request error
func SendBadRequest(w http.ResponseWriter, message string) {
	err := NewAPIError(ErrCodeBadRequest, message)
	SendError(w, err)
}

// SendUnauthorized sends an unauthorized error
func SendUnauthorized(w http.ResponseWriter, message string) {
	err := NewAPIError(ErrCodeUnauthorized, message)
	SendError(w, err)
}

// SendNotFound sends a not found error
func SendNotFound(w http.ResponseWriter, message string) {
	err := NewAPIError(ErrCodeNotFound, message)
	SendError(w, err)
}

// SendConflict sends a conflict error
func SendConflict(w http.ResponseWriter, message string) {
	err := NewAPIError(ErrCodeConflict, message)
	SendError(w, err)
}

// SendInternalError sends an internal server error
func SendInternalError(w http.ResponseWriter, message string) {
	err := NewAPIError(ErrCodeInternal, message)
	SendError(w, err)
}

// SendServiceUnavailable sends a service unavailable error
func SendServiceUnavailable(w http.ResponseWriter, message string) {
	err := NewAPIError(ErrCodeServiceUnavail, message)
	SendError(w, err)
}

// HandlePanic recovers from panics and sends an appropriate error response
func HandlePanic(w http.ResponseWriter, r *http.Request) {
	if rec := recover(); rec != nil {
		log.Printf("Panic in %s %s: %v", r.Method, r.URL.Path, rec)
		
		err := NewAPIError(ErrCodeInternal, "An unexpected error occurred")
		SendError(w, err)
	}
}

// LogError logs an error with context
func LogError(err error, context string, requestID string) {
	log.Printf("[ERROR] %s (RequestID: %s): %v", context, requestID, err)
}

// LogWarning logs a warning with context
func LogWarning(message string, context string, requestID string) {
	log.Printf("[WARNING] %s (RequestID: %s): %s", context, requestID, message)
}

// IsClientError returns true if the error is a client error (4xx)
func IsClientError(code ErrorCode) bool {
	switch code {
	case ErrCodeBadRequest, ErrCodeUnauthorized, ErrCodeForbidden, 
		 ErrCodeNotFound, ErrCodeConflict, ErrCodeValidation, ErrCodeTooManyReqs:
		return true
	default:
		return false
	}
}

// IsServerError returns true if the error is a server error (5xx)
func IsServerError(code ErrorCode) bool {
	return !IsClientError(code)
}

// WrapError wraps a standard error into an APIError
func WrapError(err error, code ErrorCode, message string) *APIError {
	apiErr := NewAPIError(code, message)
	if err != nil {
		apiErr.WithDetails(err.Error())
	}
	return apiErr
}

// Common error constructors for frequently used errors
var (
	ErrInvalidJSON = NewAPIError(ErrCodeBadRequest, "Invalid JSON in request body")
	ErrMissingAuth = NewAPIError(ErrCodeUnauthorized, "Authentication required")
	ErrInvalidAuth = NewAPIError(ErrCodeUnauthorized, "Invalid authentication credentials")
	ErrAccessDenied = NewAPIError(ErrCodeForbidden, "Access denied")
	ErrResourceNotFound = NewAPIError(ErrCodeNotFound, "Resource not found")
	ErrResourceExists = NewAPIError(ErrCodeConflict, "Resource already exists")
	ErrInternalServer = NewAPIError(ErrCodeInternal, "Internal server error")
	ErrServiceDown = NewAPIError(ErrCodeServiceUnavail, "Service temporarily unavailable")
)
