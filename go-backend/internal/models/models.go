package models

import "time"

// Response represents a standard API response structure
type Response struct {
	Message string `json:"message"`
}

// LogEntry represents a structured log message with context
type LogEntry struct {
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Context   map[string]interface{} `json:"context"`
	RequestID string                 `json:"request_id"`
	UserID    string                 `json:"user_id"`
	IP        string                 `json:"ip"`
	UserAgent string                 `json:"user_agent"`
	Method    string                 `json:"method"`
	Path      string                 `json:"path"`
	Status    int                    `json:"status"`
	Latency   int                    `json:"latency"`
}

// Rule represents a URL rule with metadata
type Rule struct {
	URL         string `json:"url" validate:"required,url"`
	Enabled     bool   `json:"enabled"`
	IsSecure    bool   `json:"is_secure,omitempty"`
	CreatedAt   string `json:"created_at,omitempty"`
	UpdatedAt   string `json:"updated_at,omitempty"`
	CreatedBy   string `json:"created_by,omitempty"`
	Description string `json:"description,omitempty"`
}

// Todo represents a todo item with its properties and metadata
type Todo struct {
	ID         int       `json:"id"`
	Title      string    `json:"title" validate:"required,min=1,max=255"`
	Content    string    `json:"content,omitempty"`
	Completed  bool      `json:"completed"`
	DueDate    time.Time `json:"due_date,omitempty"`
	CategoryID int       `json:"category_id,omitempty"`
	Tags       []string  `json:"tags,omitempty"`
	CreatedAt  string    `json:"created_at,omitempty"`
	UpdatedAt  string    `json:"updated_at,omitempty"`
}

// Category represents a category for organizing todos
type Category struct {
	ID          int    `json:"id"`
	Name        string `json:"name" validate:"required,min=1,max=100"`
	Description string `json:"description,omitempty"`
	CreatedAt   string `json:"created_at,omitempty"`
	UpdatedAt   string `json:"updated_at,omitempty"`
}

// Tag represents a tag that can be associated with todos
type Tag struct {
	ID        int    `json:"id"`
	Name      string `json:"name" validate:"required,min=1,max=50"`
	Color     string `json:"color,omitempty"`
	CreatedAt string `json:"created_at,omitempty"`
	UpdatedAt string `json:"updated_at,omitempty"`
}

// AxurePath represents a managed Axure folder path record
type AxurePath struct {
	ID        int       `json:"id"`
	Name      string    `json:"name" validate:"required,min=1,max=255"`
	Path      string    `json:"path" validate:"required,min=1"`
	CreatedAt time.Time `json:"created_at,omitempty"`
	UpdatedAt time.Time `json:"updated_at,omitempty"`
}

// DBConfig holds database configuration
type DBConfig struct {
	MySQL struct {
		Host            string        `yaml:"host"`
		Port            int           `yaml:"port"`
		User            string        `yaml:"user"`
		Password        string        `yaml:"password"`
		Database        string        `yaml:"database"`
		MaxOpenConns    int           `yaml:"max_open_conns"`
		MaxIdleConns    int           `yaml:"max_idle_conns"`
		ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	} `yaml:"mysql"`
	Redis struct {
		Host        string        `yaml:"host"`
		Port        int           `yaml:"port"`
		Password    string        `yaml:"password"`
		DB          int           `yaml:"db"`
		MaxIdle     int           `yaml:"max_idle"`
		MaxActive   int           `yaml:"max_active"`
		IdleTimeout time.Duration `yaml:"idle_timeout"`
	} `yaml:"redis"`
}
