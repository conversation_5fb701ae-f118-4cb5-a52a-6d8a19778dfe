# Internal Packages / 内部包说明

> For code usage examples, see the `services/`, `handlers/`, and `middleware/` directories. 本文档面向后端开发者，代码示例可参考 `services/`、`handlers/`、`middleware/` 目录。

This directory contains the internal packages for the Go backend API service. These packages are organized following Go best practices and clean architecture principles.

## Package Structure

### `/config`
Configuration management package that handles loading and parsing of application configuration from files and environment variables.

**Key Features:**
- YAML configuration file support
- Environment variable overrides
- Default value fallbacks
- Type-safe configuration structs

### `/database`
Database connection and management package providing singleton access to MySQL and Redis connections.

**Key Features:**
- Connection pooling
- Health checks
- Graceful shutdown
- Thread-safe access

### `/models`
Data models and structures used throughout the application.

**Key Features:**
- JSON serialization tags
- Validation tags
- Consistent field naming
- Comprehensive documentation

### `/services`
Business logic layer containing service implementations for different domains.

**Key Features:**
- Database operations
- Business rule enforcement
- Transaction management
- Error handling

**Services:**
- `RulesService`: URL rules management
- `TodosService`: Todo items management

### `/handlers`
HTTP request handlers implementing the REST API endpoints.

**Key Features:**
- Input validation
- Error handling
- Response formatting
- Dependency injection

**Handler Groups:**
- Basic handlers (health checks, ping)
- Rules handlers (CRUD operations)
- Todos handlers (CRUD operations)

### `/middleware`
HTTP middleware components for cross-cutting concerns.

**Key Features:**
- Request logging
- Authentication
- Rate limiting
- CORS handling
- Security headers
- Request size limits

**Middleware Components:**
- `AuthMiddleware`: API key authentication
- `LoggingMiddleware`: Request/response logging
- `RateLimitMiddleware`: Rate limiting protection
- `CORSMiddleware`: Cross-origin resource sharing
- `SecurityHeadersMiddleware`: Security headers
- `RequestIDMiddleware`: Request tracking

### `/validation`
Input validation and sanitization utilities.

**Key Features:**
- Model validation
- Field-level validation
- Custom validation rules
- Input sanitization
- Validation error collection

### `/errors`
Standardized error handling and response formatting.

**Key Features:**
- Consistent error responses
- HTTP status code mapping
- Error categorization
- Request ID tracking
- Validation error formatting

## Architecture Principles

### Dependency Injection
Services and handlers receive their dependencies through constructor functions, making testing easier and reducing coupling.

### Error Handling
All errors are handled consistently using the errors package, providing standardized error responses with appropriate HTTP status codes.

### Validation
Input validation is performed at multiple levels:
1. JSON unmarshaling validation
2. Field-level validation using the validation package
3. Business rule validation in services

### Security
Multiple security measures are implemented:
- API key authentication
- Rate limiting
- Input sanitization
- Security headers
- Request size limits
- CORS protection

### Logging
Comprehensive logging with:
- Request/response logging
- Error logging with context
- Request ID tracking
- Performance metrics

## Usage Examples

### Service Usage
```go
// Create a rules service
rulesService := services.NewRulesService(db)

// Add a new rule
rule := models.Rule{
    URL:     "https://example.com",
    Enabled: true,
}
err := rulesService.AddRule(rule)
```

### Handler Usage
```go
// Register handlers with dependency injection
handlers.RegisterAPIHandlers(router, database)
```

### Middleware Usage
```go
// Apply middleware chain
handler = middleware.RequestIDMiddleware(handler)
handler = middleware.LoggingMiddleware(handler)
handler = middleware.AuthMiddleware(handler, apiKey)
```

## Testing

Each package should include comprehensive unit tests covering:
- Happy path scenarios
- Error conditions
- Edge cases
- Validation rules

## Contributing

When adding new functionality:
1. Follow the existing package structure
2. Add comprehensive documentation
3. Include unit tests
4. Use dependency injection
5. Handle errors consistently
6. Validate all inputs
7. Add appropriate logging
