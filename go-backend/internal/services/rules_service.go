// Package services provides business logic implementations for different domains.
// Services handle database operations, business rule enforcement, and transaction management.
package services

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/LosEcher/lot2extension/go-backend/internal/models"
)

// RulesService handles business logic for rules management
type RulesService struct {
	db *sql.DB
}

// NewRulesService creates a new rules service instance
func NewRulesService(db *sql.DB) *RulesService {
	return &RulesService{db: db}
}

// GetAllRules retrieves all rules from the database
func (s *RulesService) GetAllRules() ([]models.Rule, error) {
	query := `
		SELECT url, enabled, is_secure, created_at, updated_at, created_by, description 
		FROM rules 
		ORDER BY created_at DESC
	`

	rows, err := s.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query rules: %w", err)
	}
	defer rows.Close()

	var rules []models.Rule
	for rows.Next() {
		var rule models.Rule
		var createdAt, updatedAt time.Time

		err := rows.Scan(
			&rule.URL,
			&rule.Enabled,
			&rule.IsSecure,
			&createdAt,
			&updatedAt,
			&rule.CreatedBy,
			&rule.Description,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan rule: %w", err)
		}

		rule.CreatedAt = createdAt.Format(time.RFC3339)
		rule.UpdatedAt = updatedAt.Format(time.RFC3339)
		rules = append(rules, rule)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating rules: %w", err)
	}

	return rules, nil
}

// AddRule adds a new rule to the database
func (s *RulesService) AddRule(rule models.Rule) error {
	// Validate required fields
	if rule.URL == "" {
		return fmt.Errorf("URL is required")
	}

	// Check if rule already exists
	exists, err := s.ruleExists(rule.URL)
	if err != nil {
		return fmt.Errorf("failed to check if rule exists: %w", err)
	}
	if exists {
		return fmt.Errorf("rule with URL %s already exists", rule.URL)
	}

	query := `
		INSERT INTO rules (url, enabled, is_secure, created_by, description, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, NOW(), NOW())
	`

	_, err = s.db.Exec(query, rule.URL, rule.Enabled, rule.IsSecure, rule.CreatedBy, rule.Description)
	if err != nil {
		return fmt.Errorf("failed to insert rule: %w", err)
	}

	return nil
}

// UpdateRule updates an existing rule in the database
func (s *RulesService) UpdateRule(rule models.Rule) error {
	if rule.URL == "" {
		return fmt.Errorf("URL is required")
	}

	// Check if rule exists
	exists, err := s.ruleExists(rule.URL)
	if err != nil {
		return fmt.Errorf("failed to check if rule exists: %w", err)
	}
	if !exists {
		return fmt.Errorf("rule with URL %s not found", rule.URL)
	}

	query := `
		UPDATE rules 
		SET enabled = ?, is_secure = ?, description = ?, updated_at = NOW()
		WHERE url = ?
	`

	result, err := s.db.Exec(query, rule.Enabled, rule.IsSecure, rule.Description, rule.URL)
	if err != nil {
		return fmt.Errorf("failed to update rule: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no rule found with URL %s", rule.URL)
	}

	return nil
}

// DeleteRule removes a rule from the database
func (s *RulesService) DeleteRule(url string) error {
	if url == "" {
		return fmt.Errorf("URL is required")
	}

	query := `DELETE FROM rules WHERE url = ?`

	result, err := s.db.Exec(query, url)
	if err != nil {
		return fmt.Errorf("failed to delete rule: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no rule found with URL %s", url)
	}

	return nil
}

// GetRule retrieves a specific rule by URL
func (s *RulesService) GetRule(url string) (*models.Rule, error) {
	if url == "" {
		return nil, fmt.Errorf("URL is required")
	}

	query := `
		SELECT url, enabled, is_secure, created_at, updated_at, created_by, description 
		FROM rules 
		WHERE url = ?
	`

	var rule models.Rule
	var createdAt, updatedAt time.Time

	err := s.db.QueryRow(query, url).Scan(
		&rule.URL,
		&rule.Enabled,
		&rule.IsSecure,
		&createdAt,
		&updatedAt,
		&rule.CreatedBy,
		&rule.Description,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("rule with URL %s not found", url)
		}
		return nil, fmt.Errorf("failed to query rule: %w", err)
	}

	rule.CreatedAt = createdAt.Format(time.RFC3339)
	rule.UpdatedAt = updatedAt.Format(time.RFC3339)

	return &rule, nil
}

// ruleExists checks if a rule with the given URL exists
func (s *RulesService) ruleExists(url string) (bool, error) {
	query := `SELECT COUNT(*) FROM rules WHERE url = ?`

	var count int
	err := s.db.QueryRow(query, url).Scan(&count)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetRulesCount returns the total number of rules
func (s *RulesService) GetRulesCount() (int, error) {
	query := `SELECT COUNT(*) FROM rules`

	var count int
	err := s.db.QueryRow(query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count rules: %w", err)
	}

	return count, nil
}
