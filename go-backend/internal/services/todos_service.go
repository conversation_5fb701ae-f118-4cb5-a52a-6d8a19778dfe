package services

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/LosEcher/lot2extension/go-backend/internal/models"
)

// TodosService handles business logic for todos management
type TodosService struct {
	db *sql.DB
}

// NewTodosService creates a new todos service instance
func NewTodosService(db *sql.DB) *TodosService {
	return &TodosService{db: db}
}

// GetAllTodos retrieves all todos from the database
func (s *TodosService) GetAllTodos() ([]models.Todo, error) {
	query := `
		SELECT t.id, t.title, t.content, t.completed, t.due_date, t.category_id, 
		       t.created_at, t.updated_at,
		       GROUP_CONCAT(tag.name) as tags
		FROM todos t
		LEFT JOIN todo_tags tt ON t.id = tt.todo_id
		LEFT JOIN tags tag ON tt.tag_id = tag.id
		GROUP BY t.id
		ORDER BY t.created_at DESC
	`

	rows, err := s.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query todos: %w", err)
	}
	defer rows.Close()

	var todos []models.Todo
	for rows.Next() {
		var todo models.Todo
		var dueDate sql.NullTime
		var createdAt, updatedAt time.Time
		var tagsStr sql.NullString

		err := rows.Scan(
			&todo.ID,
			&todo.Title,
			&todo.Content,
			&todo.Completed,
			&dueDate,
			&todo.CategoryID,
			&createdAt,
			&updatedAt,
			&tagsStr,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan todo: %w", err)
		}

		if dueDate.Valid {
			todo.DueDate = dueDate.Time
		}

		todo.CreatedAt = createdAt.Format(time.RFC3339)
		todo.UpdatedAt = updatedAt.Format(time.RFC3339)

		// Parse tags
		if tagsStr.Valid && tagsStr.String != "" {
			todo.Tags = strings.Split(tagsStr.String, ",")
		}

		todos = append(todos, todo)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating todos: %w", err)
	}

	return todos, nil
}

// AddTodo adds a new todo to the database
func (s *TodosService) AddTodo(todo models.Todo) (*models.Todo, error) {
	// Validate required fields
	if todo.Title == "" {
		return nil, fmt.Errorf("title is required")
	}

	// Start transaction
	tx, err := s.db.Begin()
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Insert todo
	query := `
		INSERT INTO todos (title, content, completed, due_date, category_id, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, NOW(), NOW())
	`

	var dueDate interface{}
	if !todo.DueDate.IsZero() {
		dueDate = todo.DueDate
	}

	result, err := tx.Exec(query, todo.Title, todo.Content, todo.Completed, dueDate, todo.CategoryID)
	if err != nil {
		return nil, fmt.Errorf("failed to insert todo: %w", err)
	}

	todoID, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get todo ID: %w", err)
	}

	// Add tags if provided
	if len(todo.Tags) > 0 {
		if err := s.addTodoTags(tx, int(todoID), todo.Tags); err != nil {
			return nil, fmt.Errorf("failed to add tags: %w", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return the created todo
	return s.GetTodo(int(todoID))
}

// UpdateTodo updates an existing todo in the database
func (s *TodosService) UpdateTodo(todo models.Todo) (*models.Todo, error) {
	if todo.ID == 0 {
		return nil, fmt.Errorf("todo ID is required")
	}

	if todo.Title == "" {
		return nil, fmt.Errorf("title is required")
	}

	// Start transaction
	tx, err := s.db.Begin()
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Update todo
	query := `
		UPDATE todos 
		SET title = ?, content = ?, completed = ?, due_date = ?, category_id = ?, updated_at = NOW()
		WHERE id = ?
	`

	var dueDate interface{}
	if !todo.DueDate.IsZero() {
		dueDate = todo.DueDate
	}

	result, err := tx.Exec(query, todo.Title, todo.Content, todo.Completed, dueDate, todo.CategoryID, todo.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to update todo: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return nil, fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return nil, fmt.Errorf("todo with ID %d not found", todo.ID)
	}

	// Update tags
	if err := s.updateTodoTags(tx, todo.ID, todo.Tags); err != nil {
		return nil, fmt.Errorf("failed to update tags: %w", err)
	}

	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return the updated todo
	return s.GetTodo(todo.ID)
}

// DeleteTodo removes a todo from the database
func (s *TodosService) DeleteTodo(id int) error {
	if id == 0 {
		return fmt.Errorf("todo ID is required")
	}

	// Start transaction
	tx, err := s.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Delete todo tags first
	if _, err := tx.Exec("DELETE FROM todo_tags WHERE todo_id = ?", id); err != nil {
		return fmt.Errorf("failed to delete todo tags: %w", err)
	}

	// Delete todo
	result, err := tx.Exec("DELETE FROM todos WHERE id = ?", id)
	if err != nil {
		return fmt.Errorf("failed to delete todo: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("todo with ID %d not found", id)
	}

	return tx.Commit()
}

// GetTodo retrieves a specific todo by ID
func (s *TodosService) GetTodo(id int) (*models.Todo, error) {
	if id == 0 {
		return nil, fmt.Errorf("todo ID is required")
	}

	query := `
		SELECT t.id, t.title, t.content, t.completed, t.due_date, t.category_id, 
		       t.created_at, t.updated_at,
		       GROUP_CONCAT(tag.name) as tags
		FROM todos t
		LEFT JOIN todo_tags tt ON t.id = tt.todo_id
		LEFT JOIN tags tag ON tt.tag_id = tag.id
		WHERE t.id = ?
		GROUP BY t.id
	`

	var todo models.Todo
	var dueDate sql.NullTime
	var createdAt, updatedAt time.Time
	var tagsStr sql.NullString

	err := s.db.QueryRow(query, id).Scan(
		&todo.ID,
		&todo.Title,
		&todo.Content,
		&todo.Completed,
		&dueDate,
		&todo.CategoryID,
		&createdAt,
		&updatedAt,
		&tagsStr,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("todo with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to query todo: %w", err)
	}

	if dueDate.Valid {
		todo.DueDate = dueDate.Time
	}

	todo.CreatedAt = createdAt.Format(time.RFC3339)
	todo.UpdatedAt = updatedAt.Format(time.RFC3339)

	// Parse tags
	if tagsStr.Valid && tagsStr.String != "" {
		todo.Tags = strings.Split(tagsStr.String, ",")
	}

	return &todo, nil
}

// addTodoTags adds tags to a todo within a transaction
func (s *TodosService) addTodoTags(tx *sql.Tx, todoID int, tags []string) error {
	for _, tagName := range tags {
		if tagName == "" {
			continue
		}

		// Get or create tag
		tagID, err := s.getOrCreateTag(tx, tagName)
		if err != nil {
			return err
		}

		// Link todo and tag
		_, err = tx.Exec("INSERT IGNORE INTO todo_tags (todo_id, tag_id) VALUES (?, ?)", todoID, tagID)
		if err != nil {
			return fmt.Errorf("failed to link todo and tag: %w", err)
		}
	}

	return nil
}

// updateTodoTags updates tags for a todo within a transaction
func (s *TodosService) updateTodoTags(tx *sql.Tx, todoID int, tags []string) error {
	// Remove existing tags
	if _, err := tx.Exec("DELETE FROM todo_tags WHERE todo_id = ?", todoID); err != nil {
		return fmt.Errorf("failed to remove existing tags: %w", err)
	}

	// Add new tags
	return s.addTodoTags(tx, todoID, tags)
}

// getOrCreateTag gets an existing tag or creates a new one
func (s *TodosService) getOrCreateTag(tx *sql.Tx, name string) (int, error) {
	// Try to get existing tag
	var tagID int
	err := tx.QueryRow("SELECT id FROM tags WHERE name = ?", name).Scan(&tagID)
	if err == nil {
		return tagID, nil
	}

	if err != sql.ErrNoRows {
		return 0, fmt.Errorf("failed to query tag: %w", err)
	}

	// Create new tag
	result, err := tx.Exec("INSERT INTO tags (name, created_at, updated_at) VALUES (?, NOW(), NOW())", name)
	if err != nil {
		return 0, fmt.Errorf("failed to create tag: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get tag ID: %w", err)
	}

	return int(id), nil
}
