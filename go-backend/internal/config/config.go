package config

import (
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config holds the application configuration
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	Database DatabaseConfig `yaml:"database"`
	Redis    RedisConfig    `yaml:"redis"`
	Security SecurityConfig `yaml:"security"`
}

// ServerConfig holds server-specific configuration
type ServerConfig struct {
	Port         int           `yaml:"port"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	MySQL MySQLConfig `yaml:"mysql"`
}

// MySQLConfig holds MySQL-specific configuration
type MySQLConfig struct {
	Host            string        `yaml:"host"`
	Port            int           `yaml:"port"`
	User            string        `yaml:"user"`
	Password        string        `yaml:"password"`
	Database        string        `yaml:"database"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
}

// RedisConfig holds Redis configuration
type RedisConfig struct {
	Host        string        `yaml:"host"`
	Port        int           `yaml:"port"`
	Password    string        `yaml:"password"`
	DB          int           `yaml:"db"`
	MaxIdle     int           `yaml:"max_idle"`
	MaxActive   int           `yaml:"max_active"`
	IdleTimeout time.Duration `yaml:"idle_timeout"`
}

// SecurityConfig holds security-related configuration
type SecurityConfig struct {
	APIKey           string   `yaml:"api_key"`
	AllowedCORS      []string `yaml:"allowed_cors"`
	AllowedBaseDir   string   `yaml:"allowed_base_dir"`
}

// LoadConfig loads configuration from file and environment variables
func LoadConfig(configPath string) (*Config, error) {
	// Set defaults
	config := &Config{
		Server: ServerConfig{
			Port:         8086,
			ReadTimeout:  10 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  120 * time.Second,
		},
		Database: DatabaseConfig{
			MySQL: MySQLConfig{
				Host:            "localhost",
				Port:            3306,
				MaxOpenConns:    25,
				MaxIdleConns:    5,
				ConnMaxLifetime: 5 * time.Minute,
			},
		},
		Redis: RedisConfig{
			Host:        "localhost",
			Port:        6379,
			DB:          0,
			MaxIdle:     10,
			MaxActive:   100,
			IdleTimeout: 240 * time.Second,
		},
		Security: SecurityConfig{
			AllowedCORS: []string{"chrome-extension://*"},
		},
	}

	// Load from file if it exists
	if configPath != "" {
		if data, err := os.ReadFile(configPath); err == nil {
			if err := yaml.Unmarshal(data, config); err != nil {
				return nil, err
			}
		}
	}

	// Override with environment variables
	if apiKey := os.Getenv("API_SECRET_KEY"); apiKey != "" {
		config.Security.APIKey = apiKey
	}

	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.Database.MySQL.Host = dbHost
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.Database.MySQL.User = dbUser
	}

	if dbPassword := os.Getenv("DB_PASSWORD"); dbPassword != "" {
		config.Database.MySQL.Password = dbPassword
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.Database.MySQL.Database = dbName
	}

	if redisHost := os.Getenv("REDIS_HOST"); redisHost != "" {
		config.Redis.Host = redisHost
	}

	if redisPassword := os.Getenv("REDIS_PASSWORD"); redisPassword != "" {
		config.Redis.Password = redisPassword
	}

	if allowedBaseDir := os.Getenv("ALLOWED_BASE_DIR"); allowedBaseDir != "" {
		config.Security.AllowedBaseDir = allowedBaseDir
	}

	return config, nil
}
