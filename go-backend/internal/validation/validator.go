package validation

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/LosEcher/lot2extension/go-backend/internal/models"
)

// ValidationError represents a validation error with field and message
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ValidationErrors represents a collection of validation errors
type ValidationErrors []ValidationError

// Error implements the error interface
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return ""
	}
	
	var messages []string
	for _, err := range ve {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}
	return strings.Join(messages, "; ")
}

// HasErrors returns true if there are validation errors
func (ve ValidationErrors) HasErrors() bool {
	return len(ve) > 0
}

// Add adds a new validation error
func (ve *ValidationErrors) Add(field, message string) {
	*ve = append(*ve, ValidationError{Field: field, Message: message})
}

// ValidateRule validates a rule model
func ValidateRule(rule models.Rule) ValidationErrors {
	var errors ValidationErrors

	// Validate URL
	if rule.URL == "" {
		errors.Add("url", "URL is required")
	} else {
		if !isValidURL(rule.URL) {
			errors.Add("url", "URL must be a valid URL")
		}
		if len(rule.URL) > 2048 {
			errors.Add("url", "URL must be less than 2048 characters")
		}
	}

	// Validate description length
	if len(rule.Description) > 500 {
		errors.Add("description", "Description must be less than 500 characters")
	}

	// Validate created_by length
	if len(rule.CreatedBy) > 100 {
		errors.Add("created_by", "Created by must be less than 100 characters")
	}

	return errors
}

// ValidateTodo validates a todo model
func ValidateTodo(todo models.Todo) ValidationErrors {
	var errors ValidationErrors

	// Validate title
	if todo.Title == "" {
		errors.Add("title", "Title is required")
	} else {
		if len(todo.Title) > 255 {
			errors.Add("title", "Title must be less than 255 characters")
		}
	}

	// Validate content length
	if len(todo.Content) > 2000 {
		errors.Add("content", "Content must be less than 2000 characters")
	}

	// Validate due date
	if !todo.DueDate.IsZero() && todo.DueDate.Before(time.Now().AddDate(-1, 0, 0)) {
		errors.Add("due_date", "Due date cannot be more than 1 year in the past")
	}

	// Validate category ID
	if todo.CategoryID < 0 {
		errors.Add("category_id", "Category ID must be positive")
	}

	// Validate tags
	if len(todo.Tags) > 10 {
		errors.Add("tags", "Maximum 10 tags allowed")
	}

	for i, tag := range todo.Tags {
		if tag == "" {
			errors.Add(fmt.Sprintf("tags[%d]", i), "Tag name cannot be empty")
		} else if len(tag) > 50 {
			errors.Add(fmt.Sprintf("tags[%d]", i), "Tag name must be less than 50 characters")
		}
	}

	return errors
}

// ValidateCategory validates a category model
func ValidateCategory(category models.Category) ValidationErrors {
	var errors ValidationErrors

	// Validate name
	if category.Name == "" {
		errors.Add("name", "Name is required")
	} else {
		if len(category.Name) > 100 {
			errors.Add("name", "Name must be less than 100 characters")
		}
		if !isValidCategoryName(category.Name) {
			errors.Add("name", "Name contains invalid characters")
		}
	}

	// Validate description length
	if len(category.Description) > 500 {
		errors.Add("description", "Description must be less than 500 characters")
	}

	return errors
}

// ValidateTag validates a tag model
func ValidateTag(tag models.Tag) ValidationErrors {
	var errors ValidationErrors

	// Validate name
	if tag.Name == "" {
		errors.Add("name", "Name is required")
	} else {
		if len(tag.Name) > 50 {
			errors.Add("name", "Name must be less than 50 characters")
		}
		if !isValidTagName(tag.Name) {
			errors.Add("name", "Name contains invalid characters")
		}
	}

	// Validate color (if provided)
	if tag.Color != "" && !isValidHexColor(tag.Color) {
		errors.Add("color", "Color must be a valid hex color (e.g., #FF0000)")
	}

	return errors
}

// isValidURL checks if a string is a valid URL
func isValidURL(str string) bool {
	if str == "" {
		return false
	}

	// Add protocol if missing
	testURL := str
	if !strings.HasPrefix(str, "http://") && !strings.HasPrefix(str, "https://") {
		testURL = "https://" + str
	}

	u, err := url.Parse(testURL)
	if err != nil {
		return false
	}

	// Check if it has a valid scheme and host
	if u.Scheme == "" || u.Host == "" {
		return false
	}

	// Additional validation: host should contain at least one dot or be localhost
	if u.Host != "localhost" && !strings.Contains(u.Host, ".") {
		return false
	}

	return true
}

// isValidCategoryName checks if a category name is valid
func isValidCategoryName(name string) bool {
	// Allow letters, numbers, spaces, hyphens, and underscores
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9\s\-_]+$`, name)
	return matched
}

// isValidTagName checks if a tag name is valid
func isValidTagName(name string) bool {
	// Allow letters, numbers, hyphens, and underscores (no spaces)
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9\-_]+$`, name)
	return matched
}

// isValidHexColor checks if a string is a valid hex color
func isValidHexColor(color string) bool {
	matched, _ := regexp.MatchString(`^#[0-9A-Fa-f]{6}$`, color)
	return matched
}

// ValidateID validates that an ID is positive
func ValidateID(id int, fieldName string) ValidationErrors {
	var errors ValidationErrors
	
	if id <= 0 {
		errors.Add(fieldName, fmt.Sprintf("%s must be a positive integer", fieldName))
	}
	
	return errors
}

// ValidateRequiredString validates that a string field is not empty
func ValidateRequiredString(value, fieldName string) ValidationErrors {
	var errors ValidationErrors
	
	if strings.TrimSpace(value) == "" {
		errors.Add(fieldName, fmt.Sprintf("%s is required", fieldName))
	}
	
	return errors
}

// ValidateStringLength validates string length constraints
func ValidateStringLength(value, fieldName string, minLen, maxLen int) ValidationErrors {
	var errors ValidationErrors
	
	length := len(value)
	if length < minLen {
		errors.Add(fieldName, fmt.Sprintf("%s must be at least %d characters", fieldName, minLen))
	}
	if length > maxLen {
		errors.Add(fieldName, fmt.Sprintf("%s must be less than %d characters", fieldName, maxLen))
	}
	
	return errors
}

// ValidateEmail validates an email address
func ValidateEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// SanitizeString removes potentially dangerous characters from a string
func SanitizeString(input string) string {
	// Remove null bytes and control characters
	sanitized := strings.ReplaceAll(input, "\x00", "")

	// Remove other control characters except newlines and tabs
	var result strings.Builder
	for _, r := range sanitized {
		if r >= 32 || r == '\n' || r == '\t' {
			result.WriteRune(r)
		}
	}

	// Trim spaces but preserve internal tabs and newlines
	output := result.String()
	return strings.TrimSpace(output)
}
