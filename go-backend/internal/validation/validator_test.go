package validation

import (
	"testing"
	"time"

	"github.com/LosEcher/lot2extension/go-backend/internal/models"
)

func TestValidateRule(t *testing.T) {
	tests := []struct {
		name          string
		rule          models.Rule
		expectErrors  bool
		expectedCount int
	}{
		{
			name: "valid rule",
			rule: models.Rule{
				URL:         "https://example.com",
				Enabled:     true,
				Description: "Test rule",
				CreatedBy:   "test_user",
			},
			expectErrors:  false,
			expectedCount: 0,
		},
		{
			name: "missing URL",
			rule: models.Rule{
				Enabled: true,
			},
			expectErrors:  true,
			expectedCount: 1,
		},
		{
			name: "invalid URL",
			rule: models.Rule{
				URL:     "not-a-url",
				Enabled: true,
			},
			expectErrors:  true,
			expectedCount: 1,
		},
		{
			name: "URL too long",
			rule: models.Rule{
				URL:     "https://example.com/" + string(make([]byte, 2050)),
				Enabled: true,
			},
			expectErrors:  true,
			expectedCount: 2, // URL too long + invalid URL due to length
		},
		{
			name: "description too long",
			rule: models.Rule{
				URL:         "https://example.com",
				Enabled:     true,
				Description: string(make([]byte, 501)),
			},
			expectErrors:  true,
			expectedCount: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateRule(tt.rule)
			
			if tt.expectErrors && !errors.HasErrors() {
				t.Errorf("Expected validation errors but got none")
			}
			
			if !tt.expectErrors && errors.HasErrors() {
				t.Errorf("Expected no validation errors but got: %v", errors)
			}
			
			if len(errors) != tt.expectedCount {
				t.Errorf("Expected %d errors, got %d", tt.expectedCount, len(errors))
			}
		})
	}
}

func TestValidateTodo(t *testing.T) {
	tests := []struct {
		name          string
		todo          models.Todo
		expectErrors  bool
		expectedCount int
	}{
		{
			name: "valid todo",
			todo: models.Todo{
				Title:      "Test Todo",
				Content:    "Test content",
				DueDate:    time.Now().Add(24 * time.Hour),
				CategoryID: 1,
				Tags:       []string{"test", "example"},
			},
			expectErrors:  false,
			expectedCount: 0,
		},
		{
			name: "missing title",
			todo: models.Todo{
				Content: "Test content",
			},
			expectErrors:  true,
			expectedCount: 1,
		},
		{
			name: "title too long",
			todo: models.Todo{
				Title: string(make([]byte, 256)),
			},
			expectErrors:  true,
			expectedCount: 1,
		},
		{
			name: "content too long",
			todo: models.Todo{
				Title:   "Test Todo",
				Content: string(make([]byte, 2001)),
			},
			expectErrors:  true,
			expectedCount: 1,
		},
		{
			name: "too many tags",
			todo: models.Todo{
				Title: "Test Todo",
				Tags:  []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11"},
			},
			expectErrors:  true,
			expectedCount: 1,
		},
		{
			name: "empty tag",
			todo: models.Todo{
				Title: "Test Todo",
				Tags:  []string{"valid", ""},
			},
			expectErrors:  true,
			expectedCount: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateTodo(tt.todo)
			
			if tt.expectErrors && !errors.HasErrors() {
				t.Errorf("Expected validation errors but got none")
			}
			
			if !tt.expectErrors && errors.HasErrors() {
				t.Errorf("Expected no validation errors but got: %v", errors)
			}
			
			if len(errors) != tt.expectedCount {
				t.Errorf("Expected %d errors, got %d", tt.expectedCount, len(errors))
			}
		})
	}
}

func TestIsValidURL(t *testing.T) {
	tests := []struct {
		name     string
		url      string
		expected bool
	}{
		{"valid https URL", "https://example.com", true},
		{"valid http URL", "http://example.com", true},
		{"URL without protocol", "example.com", true},
		{"invalid URL", "not-a-url", false},
		{"empty URL", "", false},
		{"URL with path", "https://example.com/path", true},
		{"URL with query", "https://example.com?query=value", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidURL(tt.url)
			if result != tt.expected {
				t.Errorf("isValidURL(%q) = %v, expected %v", tt.url, result, tt.expected)
			}
		})
	}
}

func TestSanitizeString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{"normal string", "hello world", "hello world"},
		{"string with null bytes", "hello\x00world", "helloworld"},
		{"string with control characters", "hello\x01\x02world", "helloworld"},
		{"string with newlines and tabs", "hello\nworld\t", "hello\nworld"},
		{"string with leading/trailing spaces", "  hello world  ", "hello world"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SanitizeString(tt.input)
			if result != tt.expected {
				t.Errorf("SanitizeString(%q) = %q, expected %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestValidateID(t *testing.T) {
	tests := []struct {
		name          string
		id            int
		fieldName     string
		expectErrors  bool
	}{
		{"valid positive ID", 1, "id", false},
		{"zero ID", 0, "id", true},
		{"negative ID", -1, "id", true},
		{"large positive ID", 999999, "id", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			errors := ValidateID(tt.id, tt.fieldName)
			
			if tt.expectErrors && !errors.HasErrors() {
				t.Errorf("Expected validation errors but got none")
			}
			
			if !tt.expectErrors && errors.HasErrors() {
				t.Errorf("Expected no validation errors but got: %v", errors)
			}
		})
	}
}
