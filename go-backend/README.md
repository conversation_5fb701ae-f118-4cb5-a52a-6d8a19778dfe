# Go Backend API 服务

这是一个基于 Go 语言开发的后端 API 服务，提供规则管理、待办事项管理、分类管理、标签管理以及文件服务功能。

## 功能特性

### 🔐 安全认证
- API 密钥认证机制
- 速率限制保护 (60 req/min)
- CORS 跨域支持
- 安全头部设置
- 请求大小限制 (10MB)
- 输入验证和清理

### 📋 核心功能
1. **规则管理** - URL 规则的增删改查，支持完整的 CRUD 操作
2. **待办事项管理** - 支持分类和标签的待办事项系统，包含到期日期
3. **分类管理** - 待办事项分类管理
4. **标签管理** - 灵活的标签系统
5. **文件服务** - Axure 文件服务，支持文件路径管理和访问

### 🗄️ 数据存储
- MySQL 数据库存储，支持连接池
- Redis 缓存支持
- 事务管理
- 数据一致性检查
- 健康检查

### 🏗️ 架构改进
- 清洁架构设计
- 依赖注入
- 服务层分离
- 标准化错误处理
- 请求 ID 跟踪
- 全面的输入验证
- 优雅关闭

## 项目结构

```
go-backend/
├── main.go                    # 主程序入口
├── axure.go                   # Axure 文件服务
├── config.yaml                # 数据库配置
├── init_db.sql                # 数据库初始化脚本
├── docker-compose.yml         # Docker 编排配置
├── Dockerfile                 # Docker 镜像构建
├── API_DOCUMENTATION.md       # API 文档
├── README.md                  # 项目说明文档
└── internal/                  # 内部包
    ├── README.md              # 内部包说明
    ├── config/                # 配置管理
    │   └── config.go
    ├── database/              # 数据库连接管理
    │   └── database.go
    ├── models/                # 数据模型
    │   └── models.go
    ├── services/              # 业务逻辑层
    │   ├── rules_service.go
    │   └── todos_service.go
    ├── handlers/              # HTTP 处理器
    │   ├── handlers.go
    │   ├── api_handlers.go
    │   ├── rules_handlers.go
    │   └── todos_handlers.go
    ├── middleware/            # 中间件
    │   ├── middleware.go
    │   └── ratelimit.go
    ├── validation/            # 输入验证
    │   └── validator.go
    └── errors/                # 错误处理
        └── errors.go
```

## 快速开始

### 环境要求
- Go 1.19+
- MySQL 8.0+
- Redis 6.0+
- Docker & Docker Compose (可选)

### 1. 环境配置

创建 `.env` 文件：
```bash
API_SECRET_KEY=your_secret_key_here
```

配置 `config.yaml`：
```yaml
mysql:
  host: localhost
  port: 3306
  user: root
  password: your_password
  database: dyrecords

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
```

### 2. 数据库初始化

```bash
# 连接到 MySQL
mysql -u root -p

# 执行初始化脚本
source init_db.sql
```

### 3. 运行服务

#### 方式一：直接运行
```bash
go mod tidy
go run .
```

#### 方式二：Docker Compose
```bash
docker-compose up --build
```

服务将在 `http://localhost:8086` 启动。

## API 接口文档

### 认证
所有 API 请求都需要在请求头中包含 `X-API-KEY`：
```
X-API-KEY: your_secret_key_here
```

### 基础接口

#### 健康检查
- `GET /ping` - 服务状态检查
- `GET /healthcheck` - 健康检查
- `GET /hello` - 问候接口
- `GET /echo` - 参数回显

### 规则管理 API

#### 获取所有规则
```http
GET /api/rules
```

#### 添加规则
```http
POST /api/rules/add
Content-Type: application/json

{
  "url": "https://example.com",
  "enabled": true
}
```

#### 更新规则
```http
PUT /api/rules/update
Content-Type: application/json

{
  "url": "https://example.com",
  "enabled": false
}
```

#### 删除规则
```http
DELETE /api/rules/delete?url=https://example.com
```

### 待办事项管理 API

#### 获取所有待办事项
```http
GET /api/todos
```

响应示例：
```json
{
  "success": true,
  "todos": [
    {
      "id": 1,
      "title": "完成API开发",
      "content": "完成后端API的开发和测试",
      "completed": false,
      "due_date": "2024-01-15T18:00:00Z",
      "category_id": 1,
      "tags": ["开发", "重要"]
    }
  ],
  "count": 1
}
```

#### 添加待办事项
```http
POST /api/todos/add
Content-Type: application/json

{
  "title": "新的待办事项",
  "content": "详细描述",
  "completed": false,
  "due_date": "2024-01-20T23:59:59Z",
  "category_id": 1,
  "tags": ["紧急", "开发"]
}
```

#### 更新待办事项
```http
PUT /api/todos/update
Content-Type: application/json

{
  "id": 1,
  "title": "更新的标题",
  "completed": true,
  "category_id": 2,
  "tags": ["完成"]
}
```

#### 删除待办事项
```http
DELETE /api/todos/delete?id=1
```

### 分类管理 API

#### 获取所有分类
```http
GET /api/categories
```

#### 添加分类
```http
POST /api/categories/add
Content-Type: application/json

{
  "name": "新分类"
}
```

#### 更新分类
```http
PUT /api/categories/update
Content-Type: application/json

{
  "id": 1,
  "name": "更新的分类名"
}
```

#### 删除分类
```http
DELETE /api/categories/delete?id=1
```

### 标签管理 API

#### 获取所有标签
```http
GET /api/tags
```

### Axure 文件服务 API

#### 添加文件到服务
```http
POST /axure/add
Content-Type: application/json

{
  "path": "/path/to/your/file.html",
  "endpoint": "custom-name" // 可选，自定义端点名称
}
```

#### 移除文件服务
```http
POST /axure/remove
Content-Type: application/json

{
  "endpoint": "/axure/files/filename.html"
}
```

#### 列出所有服务的文件
```http
GET /axure/list
```

响应示例：
```json
{
  "success": true,
  "files": [
    {
      "endpoint": "/axure/files/prototype.html",
      "file_path": "/Users/<USER>/projects/prototype.html",
      "access_count": 5,
      "last_access": "2024-01-10T15:30:00Z"
    }
  ],
  "count": 1
}
```

#### 获取文件详细信息
```http
GET /axure/info?endpoint=/axure/files/filename.html
```

#### 访问文件
```http
GET /axure/files/filename.html
```

## 错误处理

所有 API 错误响应都遵循统一格式：
```json
{
  "success": false,
  "error": "错误描述",
  "code": 400
}
```

常见错误码：
- `400` - 请求参数错误
- `401` - 未授权访问（API 密钥错误）
- `404` - 资源不存在
- `405` - 请求方法不允许
- `500` - 服务器内部错误

## 日志记录

服务会记录以下信息：
- 所有 HTTP 请求的方法、路径和处理时间
- 未授权的访问尝试
- 数据库操作错误
- 文件服务的访问记录

## 安全注意事项

1. **API 密钥管理**
   - 不要在代码中硬编码 API 密钥
   - 定期更换 API 密钥
   - 使用环境变量存储敏感信息

2. **CORS 配置**
   - 当前配置仅允许特定的 Chrome 扩展访问
   - 生产环境中请根据需要调整 CORS 策略

3. **数据库安全**
   - 使用强密码
   - 限制数据库访问权限
   - 定期备份数据

## 开发指南

### 添加新的 API 端点

1. 在 `api.go` 中添加处理函数
2. 在 `RegisterHandlers` 函数中注册路由
3. 确保添加适当的 CORS 处理和错误处理
4. 更新 API 文档

### 代码规范

- 使用有意义的函数和变量名
- 添加适当的注释
- 统一错误处理格式
- 记录重要操作的日志

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查数据库连接配置
   - 确认端口 8086 未被占用
   - 验证环境变量设置

2. **API 请求被拒绝**
   - 检查 `X-API-KEY` 请求头
   - 确认 API 密钥正确
   - 查看服务日志

3. **数据库连接错误**
   - 检查 MySQL 服务状态
   - 验证数据库配置信息
   - 确认数据库用户权限

4. **文件服务无法访问**
   - 检查文件路径是否存在
   - 确认文件权限
   - 查看访问日志

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基础 API 功能
- 添加 Axure 文件服务
- 完善错误处理和日志记录

---

## Clients / 客户端

- This backend serves both the React frontend (`axure-manager-ui`) and the browser extension (`extension`).
- 后端同时为 React 前端（axure-manager-ui）和浏览器扩展（extension）提供服务。

---

## API Documentation / API 文档

- For full API details, see [`API_DOCUMENTATION.md`](./API_DOCUMENTATION.md).
- 完整 API 说明请参见 [`API_DOCUMENTATION.md`](./API_DOCUMENTATION.md)。