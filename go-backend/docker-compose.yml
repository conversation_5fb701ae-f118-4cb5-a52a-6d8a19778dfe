version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: "mysql_iieDnp"
      MYSQL_DATABASE: "dyrecords"
      MYSQL_USER: "dyrecordsuser"
      MYSQL_PASSWORD: "dyrecordsuserpwd"
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:latest
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: go_backend
    environment:
      DB_HOST: mysql
      DB_USER: dyrecordsuser
      DB_PASSWORD: dyrecordsuserpwd
      DB_NAME: dyrecords
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      API_SECRET_KEY: ${API_SECRET_KEY}
      UID: ${UID}
      GID: ${GID}
    depends_on:
      - mysql
      - redis
    ports:
      - "8086:8086"
    volumes:
      - /Users/<USER>/Downloads/HTML:/mnt/axure_html
      - ./logs:/app/logs
    user: "${UID}:${GID}"
    working_dir: /app

volumes:
  mysql_data:
  redis-data: