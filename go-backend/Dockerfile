# syntax=docker/dockerfile:1

##
## Build
##
FROM golang:1.24-alpine AS build

WORKDIR /app

COPY go.mod ./
COPY go.sum ./
COPY config.yaml ./
RUN go mod download

COPY internal/ ./internal/
COPY *.go ./

# 交叉编译是指在一个平台上生成另一个平台的可执行程序。
# CGO_ENABLED 
# 默认值是 1，即默认开启 cgo，允许在 Go 代码中调用 C 代码。

# 当 CGO_ENABLED=1 进行编译时，会将文件中引用 libc 的库（比如常用的 net 包）以动态链接的方式生成目标文件；

# 当 CGO_ENABLED=0 进行编译时，则会把在目标文件中未定义的符号（如外部函数）一起链接到可执行文件中。
# 所以交叉编译时，我们需要将 CGO_ENABLED 设置为 0。
RUN CGO_ENABLED=0 GOOS=linux go build -o /docker-go-server

##
## Deploy
##
FROM alpine:3.18

# Create app directory
WORKDIR /app

# Copy the binary and config
COPY --from=build /docker-go-server /app/
COPY --from=build /app/config.yaml /app/

# Create logs directory with proper permissions
RUN mkdir -p /app/logs && \
    chown -R 1000:1000 /app/logs && \
    chmod 755 /app/logs

# Run as non-root user (1000:1000)
USER 1000

# Expose the port the app runs on
EXPOSE 8086

# Command to run the application
ENTRYPOINT ["/app/docker-go-server"]