package main

import (
	"net/http"
	"sync"
	"time"
)

// RateLimiter implements a simple token bucket rate limiter
type RateLimiter struct {
	mu       sync.Mutex
	limit    int
	rate     time.Duration
	requests map[string][]time.Time
}

// NewRateLimiter creates a new rate limiter with the specified limit and rate
func NewRateLimiter(limit int, rate time.Duration) *RateLimiter {
	return &RateLimiter{
		limit:    limit,
		rate:     rate,
		requests: make(map[string][]time.Time),
	}
}

// Allow checks if a request from the given IP is allowed
func (rl *RateLimiter) Allow(ip string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()

	// Clean up old requests
	var validRequests []time.Time
	for _, t := range rl.requests[ip] {
		if now.Sub(t) <= rl.rate {
			validRequests = append(validRequests, t)
		}
	}
	rl.requests[ip] = validRequests

	// Check if we've reached the rate limit
	if len(rl.requests[ip]) >= rl.limit {
		return false
	}

	// Record this request
	rl.requests[ip] = append(rl.requests[ip], now)
	return true
}

// RateLimitMiddleware creates a middleware that enforces rate limiting
func RateLimitMiddleware(limiter *RateLimiter) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Get the client's IP address
			ip := r.RemoteAddr
			if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
				ip = forwarded
			}

			// Check if the request is allowed
			if !limiter.Allow(ip) {
				http.Error(w, "Rate limit exceeded", http.StatusTooManyRequests)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}
