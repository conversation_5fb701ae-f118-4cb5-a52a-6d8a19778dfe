# Axure Manager UI

## Overview / 项目简介

A modern React + Vite + TailwindCSS frontend for managing Axure file paths and rules. Connects to the Go backend API for CRUD operations.

一个现代化的 React + Vite + TailwindCSS 前端，用于管理 Axure 文件路径和规则。通过 REST API 与 Go 后端交互。

---

## Features / 功能特性

- Manage Axure paths (add, edit, delete)
- View and update rules
- Responsive, clean UI
- Connects to backend API

- 管理 Axure 路径（增删改查）
- 查看和更新规则
- 响应式、简洁界面
- 通过 API 与后端通信

---

## Directory Structure / 目录结构

```
axure-manager-ui/
  src/
    components/   # React 组件
      PathList.tsx
      PathForm.tsx
    assets/       # 静态资源
    types.ts      # 类型定义
    App.tsx       # 入口组件
    main.tsx      # 应用入口
    index.css     # 全局样式
  public/         # 公共静态文件
  package.json    # 项目依赖
  vite.config.ts  # Vite 配置
  tailwind.config.js # TailwindCSS 配置
  tsconfig.json   # TypeScript 配置
```

---

## Main Dependencies / 主要依赖

- React 18
- Vite 3
- TailwindCSS 4
- TypeScript

---

## Setup & Development / 安装与开发

```bash
# Install dependencies / 安装依赖
npm install

# Start development server / 启动开发服务器
npm run dev

# Build for production / 构建生产包
npm run build
```

---

## API Usage / API 使用

- The frontend expects the backend API to be running at `http://localhost:8080` (see `vite.config.ts` for proxy settings).
- 前端默认通过代理将 `/api` 请求转发到 `http://localhost:8080`。

---

## Components / 组件说明

- `PathList.tsx`: Displays and manages the list of Axure paths.
- `PathForm.tsx`: Modal form for adding/editing a path.
- `types.ts`: TypeScript types for AxurePath, etc.

- `PathList.tsx`：显示和管理 Axure 路径列表
- `PathForm.tsx`：添加/编辑路径的弹窗表单
- `types.ts`：类型定义

---

## License / 许可证

MIT 