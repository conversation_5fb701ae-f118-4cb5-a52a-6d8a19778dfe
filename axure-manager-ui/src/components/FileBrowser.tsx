import React, { useState, useEffect } from 'react';

const API_KEY = import.meta.env.VITE_API_KEY;

type FileEntry = {
  name: string;
  isDir: boolean;
};

export default function FileBrowser({ initialPath, onBack }: { initialPath: string, onBack: () => void }) {
  const [currentPath, setCurrentPath] = useState(initialPath);
  const [entries, setEntries] = useState<FileEntry[]>([]);
  const [iframeFile, setIframeFile] = useState<string | null>(null);

  useEffect(() => {
    fetch(`/api/axure/list-files?path=${encodeURIComponent(currentPath)}`, {
      headers: { 'X-API-KEY': API_KEY }
    })
      .then(res => res.json())
      .then(data => {
        setEntries(data.files || []);
      });
  }, [currentPath]);

  const goUp = () => {
    if (currentPath === initialPath) return;
    setCurrentPath(currentPath.replace(/\\/g, '/').replace(/\/[^/]+\/?$/, '') || initialPath);
    setIframeFile(null); // Clear iframe when navigating
  };

  return (
    <div style={{ padding: 24, minHeight: '100vh', background: '#f9fafb' }}>
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
        <button onClick={onBack} style={{ marginRight: 16, background: '#2563eb', color: '#fff', border: 'none', borderRadius: 4, padding: '6px 16px', cursor: 'pointer' }}>Back to Manage List</button>
        <button onClick={goUp} disabled={currentPath === initialPath} style={{ marginRight: 16 }}>Up</button>
        <span style={{ color: '#2563eb', fontWeight: 500 }}>{currentPath}</span>
      </div>
      <ul style={{ listStyle: 'none', padding: 0 }}>
        {entries.map(entry => (
          <li key={entry.name} style={{ marginBottom: 8 }}>
            {entry.isDir ? (
              <button
                style={{ color: 'green', textDecoration: 'underline', background: 'none', border: 'none', cursor: 'pointer' }}
                onClick={() => setCurrentPath(currentPath.replace(/\/$/, '') + '/' + entry.name.replace(/\/$/, ''))}
              >
                📁 {entry.name}
              </button>
            ) : (
              <>
                <span>📄 {entry.name}</span>
                <a
                  href={`/api/axure/serve-file?file=${encodeURIComponent(entry.name)}&basePath=${encodeURIComponent(currentPath)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ marginLeft: 8 }}
                >
                  Download
                </a>
                <button
                  style={{ marginLeft: 8 }}
                  onClick={() => setIframeFile(entry.name)}
                >
                  View
                </button>
              </>
            )}
          </li>
        ))}
      </ul>
      {iframeFile && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            background: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
          }}
          onClick={() => setIframeFile(null)}
        >
          <div
            style={{
              background: '#fff',
              padding: 0,
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
              maxWidth: '90vw',
              maxHeight: '90vh',
              overflow: 'hidden',
              position: 'relative',
            }}
            onClick={e => e.stopPropagation()}
          >
            <button
              onClick={() => setIframeFile(null)}
              style={{
                position: 'absolute',
                top: 8,
                right: 8,
                zIndex: 2,
                background: '#f87171',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                padding: '4px 8px',
                cursor: 'pointer',
              }}
            >
              Close
            </button>
            <iframe
              src={`/api/axure/serve-file?file=${encodeURIComponent(iframeFile)}&basePath=${encodeURIComponent(currentPath)}`}
              title={iframeFile}
              style={{ width: '80vw', height: '80vh', border: 'none' }}
              sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
              onError={() => {
                alert('Error loading file');
                setIframeFile(null);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
} 