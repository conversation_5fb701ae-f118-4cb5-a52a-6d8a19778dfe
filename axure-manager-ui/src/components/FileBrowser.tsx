import { useState, useEffect } from 'react';

const API_KEY = import.meta.env.VITE_API_KEY;

type FileEntry = {
  name: string;
  isDir: boolean;
};

export default function FileBrowser({ initialPath, onBack }: { initialPath: string, onBack: () => void }) {
  const [currentPath, setCurrentPath] = useState(initialPath);
  const [entries, setEntries] = useState<FileEntry[]>([]);
  const [iframeFile, setIframeFile] = useState<string | null>(null);

  useEffect(() => {
    fetch(`/api/axure/list-files?path=${encodeURIComponent(currentPath)}`, {
      headers: { 'X-API-KEY': API_KEY }
    })
      .then(res => res.json())
      .then(data => {
        setEntries(data.files || []);
      });
  }, [currentPath]);

  const goUp = () => {
    if (currentPath === initialPath) return;
    setCurrentPath(currentPath.replace(/\\/g, '/').replace(/\/[^/]+\/?$/, '') || initialPath);
    setIframeFile(null); // Clear iframe when navigating
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 mb-8">
          <div className="bg-gradient-to-r from-green-600 to-blue-600 px-8 py-6 rounded-t-2xl">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">🗂️ File Browser</h1>
                <p className="text-green-100">Navigate and preview your Axure files</p>
              </div>
              <button
                onClick={onBack}
                className="bg-white text-green-600 hover:bg-green-50 font-semibold py-3 px-6 rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-105"
              >
                ← Back to Paths
              </button>
            </div>
          </div>

          {/* Navigation Bar */}
          <div className="px-8 py-4 border-b border-gray-100">
            <div className="flex items-center space-x-4">
              <button
                onClick={goUp}
                disabled={currentPath === initialPath}
                className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                  currentPath === initialPath
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                }`}
              >
                ⬆️ Up Directory
              </button>
              <div className="flex-1 bg-gray-50 rounded-lg px-4 py-2 border">
                <span className="text-gray-600 text-sm font-medium">📍 Current Path:</span>
                <span className="ml-2 font-mono text-sm text-blue-600">{currentPath}</span>
              </div>
            </div>
          </div>
        </div>

        {/* File List */}
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900">📋 Directory Contents</h2>
            <p className="text-gray-600 text-sm mt-1">{entries.length} items found</p>
          </div>

          {entries.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📂</div>
              <h3 className="text-xl font-semibold text-gray-600 mb-2">Empty Directory</h3>
              <p className="text-gray-500">No files or folders found in this location</p>
            </div>
          ) : (
            <div className="p-6">
              <div className="grid gap-4">
                {entries.map(entry => (
                  <div key={entry.name} className="bg-gray-50 rounded-xl p-4 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:border-blue-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center flex-1">
                        {entry.isDir ? (
                          <button
                            onClick={() => setCurrentPath(currentPath.replace(/\/$/, '') + '/' + entry.name.replace(/\/$/, ''))}
                            className="flex items-center text-green-700 hover:text-green-800 font-medium transition-colors duration-200"
                          >
                            <span className="text-2xl mr-3">📁</span>
                            <span className="text-lg">{entry.name}</span>
                            <span className="ml-2 text-sm text-gray-500">(folder)</span>
                          </button>
                        ) : (
                          <div className="flex items-center flex-1">
                            <span className="text-2xl mr-3">📄</span>
                            <span className="text-lg font-medium text-gray-900">{entry.name}</span>
                            <span className="ml-2 text-sm text-gray-500">
                              ({entry.name.split('.').pop()?.toUpperCase() || 'file'})
                            </span>
                          </div>
                        )}
                      </div>

                      {!entry.isDir && (
                        <div className="flex items-center space-x-3 ml-4">
                          <a
                            href={`/api/axure/serve-file?file=${encodeURIComponent(entry.name)}&basePath=${encodeURIComponent(currentPath)}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-blue-100 hover:bg-blue-200 text-blue-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                          >
                            💾 Download
                          </a>
                          <button
                            onClick={() => setIframeFile(entry.name)}
                            className="bg-green-100 hover:bg-green-200 text-green-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                          >
                            👁️ Preview
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* Preview Modal */}
      {iframeFile && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 backdrop-blur-sm"
          onClick={() => setIframeFile(null)}
        >
          <div
            className="bg-white rounded-2xl shadow-2xl max-w-[95vw] max-h-[95vh] overflow-hidden relative"
            onClick={e => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 px-6 py-4 flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-2xl mr-3">👁️</span>
                <div>
                  <h3 className="text-xl font-bold text-white">{iframeFile}</h3>
                  <p className="text-purple-100 text-sm">File Preview</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <a
                  href={`/api/axure/serve-file?file=${encodeURIComponent(iframeFile)}&basePath=${encodeURIComponent(currentPath)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                >
                  🔗 Open in New Tab
                </a>
                <button
                  onClick={() => setIframeFile(null)}
                  className="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                >
                  ✕ Close
                </button>
              </div>
            </div>

            {/* Iframe Content */}
            <div className="relative">
              <iframe
                src={`/api/axure/serve-file?file=${encodeURIComponent(iframeFile)}&basePath=${encodeURIComponent(currentPath)}`}
                title={iframeFile}
                className="w-[90vw] h-[80vh] border-none"
                sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
                onError={() => {
                  alert('Error loading file');
                  setIframeFile(null);
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 