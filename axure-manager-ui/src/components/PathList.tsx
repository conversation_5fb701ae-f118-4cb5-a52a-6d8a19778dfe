import React, { useEffect, useState, useCallback } from 'react';
import { AxurePath } from '../types';
import PathForm from './PathForm';
import FileBrowser from './FileBrowser';

const PathList: React.FC = () => {
  const [paths, setPaths] = useState<AxurePath[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPath, setEditingPath] = useState<AxurePath | null>(null);
  const [browsePath, setBrowsePath] = useState<string | null>(null);

  const API_KEY = import.meta.env.VITE_API_KEY;

  const fetchPaths = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/axure/paths', {
        method: 'GET',
        headers: {
          'X-API-KEY': API_KEY,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch paths');
      }
      const data = await response.json();
      setPaths(data || []);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
    } finally {
      setLoading(false);
    }
  }, [API_KEY]);

  useEffect(() => {
    fetchPaths();
  }, [fetchPaths]);

  const handleAdd = () => {
    setEditingPath(null);
    setIsFormOpen(true);
  };

  const handleEdit = (path: AxurePath) => {
    setEditingPath(path);
    setIsFormOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this path?')) {
      try {
        const response = await fetch(`/api/axure/paths/${id}`, {
          method: 'DELETE',
          headers: {
            'X-API-KEY': API_KEY,
          },
        });
        if (!response.ok) {
          throw new Error('Failed to delete path');
        }
        fetchPaths(); // Refresh list
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred');
        }
      }
    }
  };

  const handleSave = async (path: AxurePath) => {
    try {
      const url = path.id ? `/api/axure/paths/${path.id}` : '/api/axure/paths';
      const method = path.id ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': API_KEY,
        },
        body: JSON.stringify(path),
      });

      if (!response.ok) {
        throw new Error('Failed to save path');
      }

      setIsFormOpen(false);
      fetchPaths(); // Refresh list
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (browsePath) {
    return <FileBrowser initialPath={browsePath} onBack={() => setBrowsePath(null)} />;
  }

  return (
    <div className="bg-white shadow-xl rounded-2xl overflow-hidden border border-gray-100">
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">📁 Project Paths</h2>
            <p className="text-blue-100">Manage your Axure prototype directories</p>
          </div>
          <button
            onClick={handleAdd}
            className="bg-white text-blue-600 hover:bg-blue-50 font-semibold py-3 px-6 rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl hover:scale-105"
          >
            ➕ Add New Path
          </button>
        </div>
      </div>

      <div className="p-8">
        {paths.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📂</div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No paths configured</h3>
            <p className="text-gray-500 mb-6">Add your first Axure project path to get started</p>
            <button
              onClick={handleAdd}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-colors duration-200"
            >
              Add Your First Path
            </button>
          </div>
        ) : (
          <div className="grid gap-6">
            {paths.map((pathObj) => (
              <div key={pathObj.id} className="bg-gray-50 rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:border-blue-200">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-xl font-semibold text-gray-900 mr-3">{pathObj.name}</h3>
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        ID: {pathObj.id}
                      </span>
                    </div>
                    <p className="text-gray-600 font-mono text-sm bg-white px-3 py-2 rounded-lg border">
                      📍 {pathObj.path}
                    </p>
                    <div className="flex items-center mt-3 text-sm text-gray-500">
                      <span className="mr-4">🕒 Created: {new Date(pathObj.created_at).toLocaleDateString()}</span>
                      <span>🔄 Updated: {new Date(pathObj.updated_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 ml-6">
                    <button
                      onClick={() => handleEdit(pathObj)}
                      className="bg-indigo-100 hover:bg-indigo-200 text-indigo-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                    >
                      ✏️ Edit
                    </button>
                    <button
                      onClick={() => handleDelete(pathObj.id)}
                      className="bg-red-100 hover:bg-red-200 text-red-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                    >
                      🗑️ Delete
                    </button>
                    <button
                      onClick={() => setBrowsePath(pathObj.path)}
                      className="bg-green-100 hover:bg-green-200 text-green-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                    >
                      🔍 Browse Files
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {isFormOpen && (
        <PathForm
          path={editingPath}
          onSave={handleSave}
          onCancel={() => setIsFormOpen(false)}
        />
      )}
    </div>
  );
};

export default PathList;