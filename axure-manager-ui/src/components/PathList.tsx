import React, { useEffect, useState, useCallback } from 'react';
import { AxurePath } from '../types';
import PathForm from './PathForm';
import FileBrowser from './FileBrowser';

const PathList: React.FC = () => {
  const [paths, setPaths] = useState<AxurePath[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPath, setEditingPath] = useState<AxurePath | null>(null);
  const [browsePath, setBrowsePath] = useState<string | null>(null);

  const API_KEY = import.meta.env.VITE_API_KEY;

  const fetchPaths = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/axure/paths', {
        method: 'GET',
        headers: {
          'X-API-KEY': API_KEY,
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch paths');
      }
      const data = await response.json();
      setPaths(data || []);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
    } finally {
      setLoading(false);
    }
  }, [API_KEY]);

  useEffect(() => {
    fetchPaths();
  }, [fetchPaths]);

  const handleAdd = () => {
    setEditingPath(null);
    setIsFormOpen(true);
  };

  const handleEdit = (path: AxurePath) => {
    setEditingPath(path);
    setIsFormOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this path?')) {
      try {
        const response = await fetch(`/api/axure/paths/${id}`, {
          method: 'DELETE',
          headers: {
            'X-API-KEY': API_KEY,
          },
        });
        if (!response.ok) {
          throw new Error('Failed to delete path');
        }
        fetchPaths(); // Refresh list
      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An unknown error occurred');
        }
      }
    }
  };

  const handleSave = async (path: AxurePath) => {
    try {
      const url = path.id ? `/api/axure/paths/${path.id}` : '/api/axure/paths';
      const method = path.id ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': API_KEY,
        },
        body: JSON.stringify(path),
      });

      if (!response.ok) {
        throw new Error('Failed to save path');
      }

      setIsFormOpen(false);
      fetchPaths(); // Refresh list
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('An unknown error occurred');
      }
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (browsePath) {
    return <FileBrowser initialPath={browsePath} onBack={() => setBrowsePath(null)} />;
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">Axure Paths</h2>
        <button onClick={handleAdd} className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          Add Path
        </button>
      </div>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Path</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {paths.map((pathObj) => (
            <tr key={pathObj.id}>
              <td className="px-6 py-4 whitespace-nowrap">{pathObj.name}</td>
              <td className="px-6 py-4 whitespace-nowrap">{pathObj.path}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button onClick={() => handleEdit(pathObj)} className="text-indigo-600 hover:text-indigo-900">Edit</button>
                <button onClick={() => handleDelete(pathObj.id)} className="text-red-600 hover:text-red-900 ml-4">Delete</button>
                <button
                  onClick={() => setBrowsePath(pathObj.path)}
                  className="ml-4 text-green-600 hover:text-green-900"
                >
                  Browse Files
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {isFormOpen && (
        <PathForm
          path={editingPath}
          onSave={handleSave}
          onCancel={() => setIsFormOpen(false)}
        />
      )}
    </div>
  );
};

export default PathList;