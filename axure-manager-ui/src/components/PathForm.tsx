import React, { useState, useEffect } from 'react';
import { AxurePath } from '../types';

interface PathFormProps {
  path: AxurePath | null;
  onSave: (path: AxurePath) => void;
  onCancel: () => void;
}

const PathForm: React.FC<PathFormProps> = ({ path, onSave, onCancel }) => {
  const [name, setName] = useState('');
  const [filePath, setFilePath] = useState('');

  useEffect(() => {
    if (path) {
      setName(path.name);
      setFilePath(path.path);
    } else {
      setName('');
      setFilePath('');
    }
  }, [path]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      id: path ? path.id : 0,
      name,
      path: filePath,
      created_at: path ? path.created_at : new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 transform transition-all">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6 rounded-t-2xl">
          <h2 className="text-3xl font-bold text-white">
            {path ? '✏️ Edit Path' : '➕ Add New Path'}
          </h2>
          <p className="text-blue-100 mt-1">
            {path ? 'Update your Axure project path' : 'Add a new Axure project directory'}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-8">
          <div className="mb-6">
            <label htmlFor="name" className="block text-gray-700 text-sm font-semibold mb-3">
              📝 Display Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              placeholder="e.g., My Awesome Prototype"
              required
            />
          </div>

          <div className="mb-8">
            <label htmlFor="path" className="block text-gray-700 text-sm font-semibold mb-3">
              📁 Project Path
            </label>
            <input
              type="text"
              id="path"
              value={filePath}
              onChange={(e) => setFilePath(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 font-mono text-sm"
              placeholder="e.g., projects/my-prototype or /absolute/path/to/project"
              required
            />
            <p className="text-gray-500 text-xs mt-2">
              💡 Use relative paths (from base directory) or absolute paths
            </p>
          </div>

          <div className="flex items-center justify-end space-x-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold rounded-xl transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl"
            >
              {path ? 'Update Path' : 'Add Path'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PathForm;