import React, { useState, useEffect } from 'react';
import { AxurePath } from '../types';

interface PathFormProps {
  path: AxurePath | null;
  onSave: (path: AxurePath) => void;
  onCancel: () => void;
}

const PathForm: React.FC<PathFormProps> = ({ path, onSave, onCancel }) => {
  const [name, setName] = useState('');
  const [filePath, setFilePath] = useState('');

  useEffect(() => {
    if (path) {
      setName(path.name);
      setFilePath(path.path);
    } else {
      setName('');
      setFilePath('');
    }
  }, [path]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      id: path ? path.id : 0,
      name,
      path: filePath,
      created_at: path ? path.created_at : new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-xl w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6">{path ? 'Edit Path' : 'Add Path'}</h2>
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-gray-700 text-sm font-bold mb-2">Name</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            />
          </div>
          <div className="mb-6">
            <label htmlFor="path" className="block text-gray-700 text-sm font-bold mb-2">Path</label>
            <input
              type="text"
              id="path"
              value={filePath}
              onChange={(e) => setFilePath(e.target.value)}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              required
            />
          </div>
          <div className="flex items-center justify-end">
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PathForm;