import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8086',
        changeOrigin: true,
        configure: (proxy, options) => {
          if (options.url === '/api/axure/paths') {
            options.headers = {
              ...options.headers,
              'Content-Type': 'application/json',
              'X-API-KEY': 'KJc3sdGO12Us8',
            };
          }
          return options;
        },
      },
    },
  },
})
